// Exemplo de como usar useRegisterForeignUser com recaptcha

"use client"

import { useState } from "react"
import { useRegisterForeignUser } from "@/utils/queries/foreign-registration"
import { useRecaptcha } from "@/utils/recaptcha"
import { toast } from "@/hooks/use-toast"

export function ExampleForeignRegistration() {
    const { executeRecaptcha } = useRecaptcha()
    const registerForeignMutation = useRegisterForeignUser()
    const [identifier, setIdentifier] = useState('')

    const handleSubmit = async () => {
        try {
            // 1. Executar recaptcha primeiro
            const recaptchaToken = await executeRecaptcha()
            
            // 2. Chamar a API com identifier e recaptcha_token
            registerForeignMutation.mutate({
                identifier: identifier,
                recaptcha_token: recaptchaToken
            }, {
                onSuccess: () => {
                    toast({
                        title: "✅ Registo estrangeiro efetuado!",
                        description: "Registo processado com sucesso.",
                        variant: "default"
                    })
                },
                onError: (error: any) => {
                    if (error?.response?.status === 409) {
                        toast({
                            title: "⚠️ Já registado",
                            description: "Este registo já foi processado anteriormente.",
                            variant: "destructive"
                        })
                    } else {
                        const errorMessage = error?.response?.data?.message ||
                                           error?.message ||
                                           "Ocorreu um erro no registo. Tente novamente."
                        
                        toast({
                            title: "❌ Erro no registo",
                            description: errorMessage,
                            variant: "destructive"
                        })
                    }
                }
            })
        } catch (error) {
            toast({
                title: "❌ Erro reCAPTCHA",
                description: "Falha na verificação reCAPTCHA. Tente novamente.",
                variant: "destructive"
            })
        }
    }

    return (
        <div>
            <input 
                value={identifier}
                onChange={(e) => setIdentifier(e.target.value)}
                placeholder="Identifier"
            />
            <button 
                onClick={handleSubmit}
                disabled={registerForeignMutation.isPending || !identifier}
            >
                {registerForeignMutation.isPending ? 'Processando...' : 'Registar Estrangeiro'}
            </button>
        </div>
    )
}
