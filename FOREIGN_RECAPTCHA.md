# Integração reCAPTCHA com useRegisterForeignUser

## ✅ Modificações Realizadas

### 1. **Tipos Atualizados** (`/utils/types/foreign-registration.ts`)
```typescript
export type ForeignRegistration = {
    identifier: string
}

export type ForeignRegistrationRequest = {
    identifier: string
    recaptcha_token: string  // ← NOVO
}
```

### 2. **Request Modificado** (`/utils/requests/foreign-registration.ts`)
```typescript
export const postForeignRegistration = async (data: ForeignRegistrationRequest) => {
    const response = await axios.post<ForeignRegistration>(
        `https://portalchega.pt/api/registrations/set-foreign-registration/`, 
        data  // ← Agora envia { identifier, recaptcha_token }
    )
    return response.data
}
```

### 3. **Query Atualizada** (`/utils/queries/foreign-registration.ts`)
```typescript
export const useRegisterForeignUser = () => {
    return useMutation({
        mutationFn: postForeignRegistration,
        onSuccess: (data) => {
            console.log('Foreign registration successful:', data)
        },
        onError: (error: any) => {
            // Não logar erro 409 (comportamento normal)
            if (error?.response?.status !== 409) {
                console.error('Foreign registration failed:', error)
            }
        }
    });
}
```

## 🎯 Como Usar no Seu Componente

```typescript
"use client"

import { useRegisterForeignUser } from "@/utils/queries/foreign-registration"
import { useRecaptcha } from "@/utils/recaptcha"

export function MeuComponente() {
    const { executeRecaptcha } = useRecaptcha()
    const registerForeignMutation = useRegisterForeignUser()

    const handleSubmit = async () => {
        try {
            // 1. Executar reCAPTCHA
            const recaptchaToken = await executeRecaptcha()
            
            // 2. Chamar API com reCAPTCHA
            registerForeignMutation.mutate({
                identifier: "seu-uuid-aqui",
                recaptcha_token: recaptchaToken
            }, {
                onSuccess: () => {
                    // Sucesso!
                },
                onError: (error: any) => {
                    if (error?.response?.status === 409) {
                        // Já registado (comportamento normal)
                    } else {
                        // Erro real
                    }
                }
            })
        } catch (error) {
            // Erro no reCAPTCHA
        }
    }

    return (
        <button onClick={handleSubmit}>
            Registar Estrangeiro
        </button>
    )
}
```

## 📡 API Call Final

**POST** `https://portalchega.pt/api/registrations/set-foreign-registration/`

**Payload:**
```json
{
    "identifier": "uuid-do-registo",
    "recaptcha_token": "token-gerado-pelo-recaptcha"
}
```

## ✨ Funcionalidades

- ✅ **reCAPTCHA integrado** antes de cada chamada API
- ✅ **Erro 409 silenciado** no console (comportamento normal)
- ✅ **Tratamento de erros** melhorado
- ✅ **Tipos TypeScript** atualizados
- ✅ **Compatível** com sistema existente
