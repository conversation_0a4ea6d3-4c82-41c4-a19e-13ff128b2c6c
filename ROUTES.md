# Rotas da Aplicação

## Páginas de Registo

### `/registration`
- **Descrição**: Página principal do formulário de registo
- **Componente**: `RegistrationForm`
- **Parâmetro obrigatório**: `?uuid=` ou `?identifier=`

### `/registration/success`
- **Descrição**: Página mostrada após registo bem-sucedido
- **Características**: 
  - Mensagem de parabéns
  - Botão "Fazer Login no Portal"
  - Link para voltar ao registo
- **Navegação**: Automática após `onSuccess` da API

### `/registration/conflict`
- **Descrição**: Página mostrada quando há dados duplicados (erro 409)
- **Características**:
  - Explicação dos possíveis motivos
  - Informações de contacto do suporte
  - Botão para tentar login no portal
  - Link para voltar ao registo
- **Navegação**: Automática após erro 409 da API

## Fluxo de Navegação

```
/registration 
    ├── Sucesso → /registration/success
    ├── Erro 409 → /registration/conflict
    └── Outros erros → Toast (permanece na página)
```

## APIs Utilizadas

- **POST** `https://portalchega.pt/api/registrations/finish-registration/`
  - **200**: Sucesso → navegar para `/registration/success`
  - **409**: Dados duplicados → navegar para `/registration/conflict`
  - **Outros**: Erro → mostrar toast

- **GET** `https://portalchega.pt/api/registrations/validate-registration/`
  - Validação do UUID antes do registo
