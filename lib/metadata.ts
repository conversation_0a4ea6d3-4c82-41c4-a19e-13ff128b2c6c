// Configurações de metadados para diferentes páginas\n\nexport const siteConfig = {\n  name: \"Portal Chega - Registo de Membros\",\n  description: \"Registe-se como membro do partido Chega. Plataforma oficial para novos registos e adesões ao movimento político que representa os valores tradicionais portugueses.\",\n  url: \"https://registo.partidochega.pt\",\n  ogImage: \"/images/logo.png\",\n  links: {\n    twitter: \"https://twitter.com/PartidoChega\",\n    facebook: \"https://facebook.com/PartidoChega\",\n    instagram: \"https://instagram.com/partidochega\",\n    website: \"https://portalchega.pt\"\n  },\n  creator: {\n    name: \"Partido Chega\",\n    twitter: \"@PartidoChega\"\n  }\n}\n\nexport const pageMetadata = {\n  home: {\n    title: \"Portal Chega - Registo de Membros\",\n    description: \"Acesse o portal oficial do partido Chega para efetuar o seu registo como membro.\"\n  },\n  registration: {\n    title: \"Formulário de Registo - Portal Chega\",\n    description: \"Complete o seu registo como membro do partido Chega. Preencha os seus dados pessoais para se juntar ao movimento.\"\n  },\n  success: {\n    title: \"Registo Concluído - Portal Chega\",\n    description: \"Parabéns! O seu registo como membro do partido Chega foi concluído com sucesso.\"\n  },\n  conflict: {\n    title: \"Dados Já Registados - Portal Chega\",\n    description: \"Os dados inseridos já se encontram registados no sistema. Contacte o suporte para assistência.\"\n  }\n}\n\nexport const structuredData = {\n  organization: {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"PoliticalParty\",\n    \"name\": \"Partido Chega\",\n    \"url\": \"https://portalchega.pt\",\n    \"logo\": \"https://registo.partidochega.pt/images/logo.png\",\n    \"description\": \"Partido político português que representa os valores tradicionais e conservadores.\",\n    \"foundingDate\": \"2019\",\n    \"founder\": {\n      \"@type\": \"Person\",\n      \"name\": \"André Ventura\"\n    },\n    \"sameAs\": [\n      \"https://twitter.com/PartidoChega\",\n      \"https://facebook.com/PartidoChega\",\n      \"https://instagram.com/partidochega\"\n    ]\n  },\n  website: {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"WebSite\",\n    \"name\": \"Portal Chega - Registo de Membros\",\n    \"url\": \"https://registo.partidochega.pt\",\n    \"description\": \"Plataforma oficial para registo de novos membros do partido Chega.\",\n    \"inLanguage\": \"pt-PT\",\n    \"publisher\": {\n      \"@type\": \"PoliticalParty\",\n      \"name\": \"Partido Chega\"\n    }\n  }\n}\n