# Componentes de Registo

## Fluxo Principal

O registo segue um fluxo condicional através de componentes integrados na mesma página `/registration`.

## Componentes de Estado

### 1. `RegistrationForm` (Principal)
- **Localização**: `/components/forms/registration/registration-form.tsx`
- **Função**: Gerencia todo o fluxo de registo
- **Estados**: `showSuccessMessage`, `showConflictMessage`, `residenceChecked`

### 2. `RegistrationSuccessMessage`
- **Localização**: `/components/forms/registration/residence-check/registration-success-message.tsx`
- **Quando aparece**: Após registo bem-sucedido (status 200)
- **Características**:
  - ✅ Mensagem de parabéns
  - 🏠 Botão "Fazer Login no Portal"
  - 🔄 Botão "Novo Registo" (opcional)

### 3. `RegistrationConflictMessage`
- **Localização**: `/components/forms/registration/residence-check/registration-conflict-message.tsx`
- **Quando aparece**: Quando dados já estão registados (erro 409)
- **Características**:
  - ⚠️ Explicação dos motivos do conflito
  - 📞 Informações de contacto do suporte
  - 🏠 Botão "Tentar Login no Portal"
  - 🔄 Botão "Tentar Novo Registo" (opcional)

## Fluxo de Estados

```
/registration
├── Loading (validation.isLoading)
├── InvalidUuidMessage (!uuid)
├── ConflictErrorMessage (validation.isConflictError)
├── ResidenceCheck (!residenceChecked)
├── NonResidentMessage (isForeignUser ou !isPortugalResident)
├── RegistrationFormCard (formulário principal)
├── RegistrationSuccessMessage (showSuccessMessage)
└── RegistrationConflictMessage (showConflictMessage)
```

## APIs Utilizadas

- **POST** `https://portalchega.pt/api/registrations/finish-registration/`
  - **200**: Sucesso → mostrar `RegistrationSuccessMessage`
  - **409**: Dados duplicados → mostrar `RegistrationConflictMessage`
  - **Outros**: Erro → mostrar toast e permanecer no formulário

- **GET** `https://portalchega.pt/api/registrations/validate-registration/`
  - Validação do UUID antes do registo

## Funcionalidades Especiais

### Reiniciar Registo
- Função `handleStartNewRegistration()` limpa todos os estados
- Permite começar um novo registo sem recarregar a página
- Acessível através dos botões "Novo Registo" nas mensagens

### Integração na Mesma Página
- Todos os componentes são renderizados condicionalmente
- Não há navegação entre páginas
- Mantém o contexto e estado da aplicação
