# ✅ reCAPTCHA Integrado no ResidenceCheck

## 🔧 Modificações Realizadas

### **Arquivo Modificado**: `/components/forms/registration/residence-check/residence-check.tsx`

## 📋 Mudanças Implementadas

### **1. Função Async + reCAPTCHA**
**Antes:**
```typescript
const handleConfirmForeignRegistration = () => {
    setShowConfirmDialog(false)
    
    foreignRegistrationMutation.mutate(uuid, {
        // ...
    })
}
```

**Depois:**
```typescript
const handleConfirmForeignRegistration = async () => {
    setShowConfirmDialog(false)
    
    try {
        // 1. Executar reCAPTCHA primeiro
        const recaptchaToken = await executeRecaptcha()
        
        // 2. Chamar API com objeto correto
        foreignRegistrationMutation.mutate({
            identifier: uuid,
            recaptcha_token: recaptchaToken
        }, {
            // ...
        })
    } catch (error) {
        // Tratamento de erro reCAPTCHA
    }
}
```

### **2. Melhor Tratamento <PERSON> Erro<PERSON>**
**✅ Erro 409 (Já Registado):**
- Mostra toast específico: "⚠️ Já registado"
- **Continua o fluxo** chamando `onResidenceConfirmed(false)`
- Comportamento: trata como "sucesso" pois o registo já existe

**✅ Erro reCAPTCHA:**
- Toast específico: "❌ Erro reCAPTCHA"
- **Não continua o fluxo** - usuário pode tentar novamente

**✅ Outros Erros:**
- Toast genérico com mensagem da API
- **Não continua o fluxo**

### **3. Payload da API Atualizado**
**Antes:**
```json
"string-uuid"
```

**Depois:**
```json
{
    "identifier": "string-uuid",
    "recaptcha_token": "token-gerado"
}
```

## 🎯 Fluxo Completo

1. **Utilizador clica "Não, não sou residente"**
2. **Dialog de confirmação aparece**
3. **Utilizador confirma no dialog**
4. **reCAPTCHA é executado automaticamente**
5. **API é chamada** com UUID + token reCAPTCHA
6. **Resposta da API:**
   - ✅ **Sucesso (200)**: Toast de sucesso + continua fluxo
   - ⚠️ **Já registado (409)**: Toast de aviso + continua fluxo  
   - ❌ **Outros erros**: Toast de erro + para fluxo
7. **Erro reCAPTCHA**: Toast de erro + para fluxo

## 🔒 Segurança

- ✅ **reCAPTCHA obrigatório** antes da API call
- ✅ **Erro 409 não bloqueia** o fluxo (comportamento normal)
- ✅ **Tratamento robusto** de todos os cenários de erro
- ✅ **UX suave** - usuário não percebe a complexidade

## 🚀 Resultado

**O componente ResidenceCheck agora usa reCAPTCHA automaticamente quando o usuário escolhe "não residente"!**

- Nenhuma mudança visível na interface
- Segurança melhorada com reCAPTCHA
- Tratamento de erros mais inteligente
- Fluxo do usuário inalterado
