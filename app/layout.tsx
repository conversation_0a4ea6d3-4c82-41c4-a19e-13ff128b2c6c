import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import { Providers } from "@/components/providers";
import { Toaster } from "@/components/ui/toaster";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Registo de Membros - Portal Chega",
  description: "Registe-se como membro do partido Chega. Plataforma oficial para novos registos e adesões ao movimento político que representa os valores tradicionais portugueses.",
  keywords: ["Chega", "partido", "registo", "membro", "política", "Portugal", "André Ventura", "adesão", "inscrição"],
  authors: [{ name: "Partido Chega" }],
  creator: "Partido Chega",
  publisher: "Partido Chega",
  robots: "index, follow",
  openGraph: {
    type: "website",
    locale: "pt_PT",
    url: "https://registo.partidochega.pt",
    title: "Registo de Membros - Portal Chega",
    description: "Registe-se como membro do partido Chega. Plataforma oficial para novos registos e adesões ao movimento político que representa os valores tradicionais portugueses.",
    siteName: "Portal Chega - Registo de Membros",
    images: [
      {
        url: "/images/logo.png",
        width: 800,
        height: 600,
        alt: "Logótipo do Partido Chega",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Registo de Membros - Portal Chega",
    description: "Registe-se como membro do partido Chega. Plataforma oficial para novos registos e adesões.",
    images: ["/images/logo.png"],
    creator: "@PartidoChega",
  },
  icons: {
    icon: "/images/logo.png",
    shortcut: "/images/logo.png",
    apple: "/images/logo.png",
  },
  manifest: "/manifest.json",
  other: {
    "theme-color": "#1a365d",
    "msapplication-TileColor": "#1a365d",
    "msapplication-TileImage": "/images/logo.png",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="pt">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <Providers>
          {children}
          <Toaster />
        </Providers>
      </body>
    </html>
  );
}
