"use client"

import Image from "next/image"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { CheckCircle, Home, ArrowLeft } from "lucide-react"
import Link from "next/link"

export default function RegistrationSuccessPage() {
    const handleGoToPortal = () => {
        window.location.href = 'https://portalchega.pt/'
    }

    return (
        <main className="min-h-screen bg-white">
            <div className="container mx-auto py-10 px-4 sm:px-6">
                <div className="flex flex-col sm:flex-row items-center justify-center sm:justify-center gap-4 mb-10">
                    <Image src="/images/logo.png" alt="Chega Logo" width={100} height={100} />
                    <h1 className="text-3xl font-bold text-center sm:text-left text-primary">
                        Registo de Membros
                    </h1>
                </div>
                
                <div className="max-w-2xl mx-auto">
                    <Card className="border-2 shadow-lg border-green-200">
                        <CardHeader className="text-center pb-6">
                            <div className="flex justify-center mb-4">
                                <div className="bg-green-100 p-4 rounded-full">
                                    <CheckCircle className="h-16 w-16 text-green-600" />
                                </div>
                            </div>
                            <CardTitle className="text-3xl font-bold text-green-700 mb-2">
                                ✅ Registo Concluído!
                            </CardTitle>
                            <CardDescription className="text-xl">
                                O seu registo foi efetuado com sucesso
                            </CardDescription>
                        </CardHeader>
                        
                        <CardContent className="space-y-6 text-center">
                            <div className="bg-green-50 p-6 rounded-lg border border-green-200">
                                <h3 className="text-2xl font-semibold mb-4 text-green-800">
                                    🎉 Bem-vindo ao Chega!
                                </h3>
                                <p className="text-gray-700 mb-4 leading-relaxed text-lg">
                                    Parabéns! O seu registo como membro foi processado com sucesso. 
                                </p>
                                <p className="text-gray-600 mb-4">
                                    Pode agora aceder ao portal dos membros para explorar todas as funcionalidades disponíveis 
                                    e participar ativamente na vida do partido.
                                </p>
                            </div>
                            
                            <div className="space-y-4">
                                <Button
                                    onClick={handleGoToPortal}
                                    size="lg"
                                    className="w-full sm:w-auto bg-primary hover:bg-primary/90 text-white font-semibold text-lg px-8 py-3"
                                >
                                    <Home className="h-5 w-5 mr-2" />
                                    Fazer Login no Portal
                                </Button>
                                
                                <div className="pt-4">
                                    <Link href="/registration" className="inline-flex items-center text-gray-600 hover:text-gray-800 text-sm">
                                        <ArrowLeft className="h-4 w-4 mr-1" />
                                        Voltar ao Registo
                                    </Link>
                                </div>
                            </div>

                            <div className="bg-blue-50 p-4 rounded-lg border border-blue-200 mt-6">
                                <p className="text-sm text-blue-700">
                                    <strong>Próximos passos:</strong> Use as mesmas credenciais (email) que utilizou neste registo 
                                    para fazer login no portal e completar a configuração da sua conta.
                                </p>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </main>
    )
}
