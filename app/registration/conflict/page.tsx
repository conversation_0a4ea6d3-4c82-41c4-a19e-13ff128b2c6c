"use client"

import Image from "next/image"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { AlertTriangle, Mail, Phone, ArrowLeft, Home } from "lucide-react"
import Link from "next/link"

export default function RegistrationConflictPage() {
    const handleGoToPortal = () => {
        window.location.href = 'https://portalchega.pt/'
    }

    return (
        <main className="min-h-screen bg-white">
            <div className="container mx-auto py-10 px-4 sm:px-6">
                <div className="flex flex-col sm:flex-row items-center justify-center sm:justify-center gap-4 mb-10">
                    <Image src="/images/logo.png" alt="Chega Logo" width={100} height={100} />
                    <h1 className="text-3xl font-bold text-center sm:text-left text-primary">
                        Registo de Membros
                    </h1>
                </div>
                
                <div className="max-w-2xl mx-auto">
                    <Card className="border-2 shadow-lg border-orange-200">
                        <CardHeader className="text-center pb-6">
                            <div className="flex justify-center mb-4">
                                <div className="bg-orange-100 p-4 rounded-full">
                                    <AlertTriangle className="h-16 w-16 text-orange-600" />
                                </div>
                            </div>
                            <CardTitle className="text-3xl font-bold text-orange-700 mb-2">
                                ⚠️ Dados Já Registados
                            </CardTitle>
                            <CardDescription className="text-xl">
                                Alguns dos dados inseridos já se encontram no sistema
                            </CardDescription>
                        </CardHeader>
                        
                        <CardContent className="space-y-6">
                            <div className="bg-orange-50 p-6 rounded-lg border border-orange-200">
                                <h3 className="text-xl font-semibold mb-4 text-orange-800 text-center">
                                    🔍 Possíveis motivos
                                </h3>
                                <ul className="text-orange-700 space-y-2 list-disc list-inside">
                                    <li><strong>Email já associado</strong> a outra conta de membro</li>
                                    <li><strong>NIF já registado</strong> no sistema</li>
                                    <li><strong>Número de telefone</strong> já em uso por outro membro</li>
                                    <li><strong>Dados pessoais</strong> que coincidem com registo existente</li>
                                </ul>
                            </div>

                            <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
                                <h3 className="text-xl font-semibold mb-4 text-blue-800 text-center">
                                    📞 Como Resolver
                                </h3>
                                <p className="text-blue-700 mb-4 text-center">
                                    Para resolver esta situação e completar o seu registo, 
                                    entre em contacto com o nosso suporte técnico:
                                </p>
                                <div className="space-y-3 text-blue-700">
                                    <div className="flex items-center justify-center gap-3 p-3 bg-white rounded-lg">
                                        <Mail className="h-5 w-5 flex-shrink-0" />
                                        <span className="font-medium"><EMAIL></span>
                                    </div>
                                    <div className="flex items-center justify-center gap-3 p-3 bg-white rounded-lg">
                                        <Phone className="h-5 w-5 flex-shrink-0" />
                                        <span className="font-medium">+351 XXX XXX XXX</span>
                                    </div>
                                </div>
                            </div>

                            <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                                <p className="text-sm text-yellow-800 text-center">
                                    <strong>Nota:</strong> Se já é membro registado, pode tentar fazer login diretamente no portal.
                                </p>
                            </div>

                            <div className="flex flex-col sm:flex-row gap-4 justify-center pt-4">
                                <Button
                                    onClick={handleGoToPortal}
                                    variant="outline"
                                    size="lg"
                                    className="font-semibold bg-primary text-white hover:bg-primary/90 border-primary"
                                >
                                    <Home className="h-4 w-4 mr-2" />
                                    Tentar Login no Portal
                                </Button>
                                
                                <Link href="/registration">
                                    <Button variant="outline" size="lg" className="w-full sm:w-auto">
                                        <ArrowLeft className="h-4 w-4 mr-2" />
                                        Voltar ao Registo
                                    </Button>
                                </Link>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </main>
    )
}
