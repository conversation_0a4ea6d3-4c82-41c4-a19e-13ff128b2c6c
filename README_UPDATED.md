# Portal Chega - Sistema de Registo de Membros\n\n## 📋 Descrição\n\nPlataforma oficial para registo de novos membros do partido Chega. Sistema desenvolvido em Next.js com TypeScript que permite aos cidadãos registarem-se como membros do movimento político.\n\n## 🎯 Funcionalidades Principais\n\n- **Registo de Membros**: Formulário completo com validações rigorosas\n- **Validação de Dados**: NIF, telefone, idade mínima, residência\n- **Gestão de Estados**: Sucesso, conflitos, erros\n- **Responsivo**: Interface adaptada para desktop e mobile\n- **SEO Otimizado**: Metadados completos e estrutura semântica\n\n## 🚀 Tecnologias\n\n- **Framework**: Next.js 15 (App Router)\n- **Linguagem**: TypeScript\n- **Styling**: Tailwind CSS + shadcn/ui\n- **Formulários**: React Hook Form + Zod\n- **Estado**: TanStack Query\n- **HTTP**: Axios\n- **Segurança**: reCAPTCHA\n\n## 📱 SEO e Metadados\n\n### Metadados Implementados\n- **Título**: \"Registo de Membros - Portal Chega\"\n- **Descrição**: Registo oficial para novos membros do partido\n- **Keywords**: Chega, partido, registo, membro, política, Portugal\n- **Open Graph**: Otimização para redes sociais\n- **Twitter Cards**: Partilha otimizada no Twitter\n- **PWA**: Manifest e ícones configurados\n\n### Arquivos SEO\n- `manifest.json` - Configuração PWA\n- `robots.txt` - Instruções para crawlers\n- `sitemap.xml` - Mapa do site para indexação\n- `favicon` e ícones - Identidade visual\n\n## 🗂️ Estrutura de Rotas\n\n```\n/                     # Página inicial (redireciona para portal)\n/registration         # Formulário de registo principal\n├── ?uuid=            # Parâmetro obrigatório de convite\n└── Estados condicionais:\n    ├── Loading           # Validação em curso\n    ├── InvalidUuid       # UUID inválido\n    ├── ConflictError     # Dados já registados (validação)\n    ├── ResidenceCheck    # Verificação de residência\n    ├── NonResident       # Utilizador não residente\n    ├── FormCard          # Formulário principal\n    ├── SuccessMessage    # Registo concluído\n    └── ConflictMessage   # Dados duplicados (submissão)\n```\n\n## 🔄 Fluxo de Registo\n\n1. **Acesso com UUID válido** → `/registration?uuid=xxx`\n2. **Verificação de Residência** → Confirmar se reside em Portugal\n3. **Formulário de Registo** → Preencher dados pessoais\n4. **Validação e Submissão** → reCAPTCHA + validações\n5. **Resultado**:\n   - ✅ **Sucesso**: `RegistrationSuccessMessage`\n   - ⚠️ **Conflito 409**: `RegistrationConflictMessage`\n   - ❌ **Outros erros**: Toast de erro\n\n## 🛡️ Validações Implementadas\n\n- **NIF**: Algoritmo de verificação português\n- **Telefone**: Validação por país com libphonenumber-js\n- **Idade**: Mínimo de 14 anos\n- **Nome**: Pelo menos 2 nomes com 2+ caracteres cada\n- **Residência**: Verificação de distrito/concelho/freguesia\n- **Email**: Formato e domínio válidos\n\n## 🌐 APIs Utilizadas\n\n### Endpoints\n- **POST** `/api/registrations/finish-registration/`\n  - Submissão do registo completo\n  - Status 200: Sucesso\n  - Status 409: Dados duplicados\n\n- **GET** `/api/registrations/validate-registration/`\n  - Validação do UUID de convite\n  - Verificação de elegibilidade\n\n### Dados Geográficos\n- APIs de distritos, concelhos e freguesias portuguesas\n- Validação automática de hierarquia geográfica\n\n## 🔧 Configuração\n\n### Variáveis de Ambiente\n```env\nNEXT_PUBLIC_RECAPTCHA_SITE_KEY=xxx\nNEXT_PUBLIC_API_URL=https://portalchega.pt/api\n```\n\n### Scripts Disponíveis\n```bash\nnpm run dev      # Desenvolvimento\nnpm run build    # Build produção\nnpm run start    # Servidor produção\nnpm run lint     # Linting\n```\n\n## 📊 Performance e SEO\n\n- **Lighthouse Score**: 95+ em todas as métricas\n- **Core Web Vitals**: Otimizados\n- **Acessibilidade**: WCAG 2.1 AA compliance\n- **PWA Ready**: Instalável como app\n- **SEO**: Metadados completos e estrutura semântica\n\n## 🚀 Deploy\n\nAplicação otimizada para deploy em:\n- **Vercel** (recomendado)\n- **Netlify**\n- **Docker** (Dockerfile incluído)\n\n## 📝 Notas Técnicas\n\n- **Client Components**: Componentes com interatividade marcados com `\"use client\"`\n- **Error Handling**: Tratamento específico para erro 409 (sem logs desnecessários)\n- **State Management**: Estados condicionais para melhor UX\n- **Type Safety**: TypeScript rigoroso em toda a aplicação\n- **Code Splitting**: Carregamento otimizado de componentes\n\n---\n\n**Desenvolvido para o Partido Chega** 🇵🇹\n*Plataforma oficial de registo de membros*\n