"use client"

import { Control, FieldPath } from "react-hook-form"
import {
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form"
import { Checkbox } from "@/components/ui/checkbox"
import { FormValues } from "../registration/schema"

interface BaseCheckboxFieldProps {
    control: Control<FormValues>
    name: FieldPath<FormValues>
    label: string
    description?: string
    required?: boolean
    disabled?: boolean
    className?: string
}

export function BaseCheckboxField({
    control,
    name,
    label,
    description,
    required = false,
    disabled = false,
    className = ""
}: BaseCheckboxFieldProps) {
    return (
        <FormField
            control={control}
            name={name}
            render={({ field }) => (
                <FormItem className={`flex flex-row items-start space-x-3 space-y-0 ${className}`}>
                    <FormControl>
                        <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled={disabled}
                        />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                        <FormLabel className={`text-sm font-normal ${required ? "after:content-['*'] after:ml-0.5 after:text-red-500" : ""}`}>
                            {label}
                        </FormLabel>
                        {description && (
                            <p className="text-sm text-muted-foreground">
                                {description}
                            </p>
                        )}
                        <FormMessage />
                    </div>
                </FormItem>
            )}
        />
    )
}
