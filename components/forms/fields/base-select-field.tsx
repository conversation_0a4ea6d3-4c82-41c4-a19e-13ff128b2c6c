"use client"

import { useState, useEffect, useRef } from "react"
import { Check, ChevronsUpDown, Search } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Command, CommandEmpty, CommandGroup, CommandItem, CommandList } from "@/components/ui/command"
import { Command as CommandPrimitive } from "cmdk"
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { FlagImage } from "@/components/ui/flagImage"
import type { SelectFieldProps } from "./types"

export function BaseSelectField({
    control,
    name,
    label,
    placeholder = "Selecione...",
    className = "",
    required = true,
    options = [],
    disabled = false,
    showFlags = false,
    flagProp = "iso",
    enableSearch = false,
    searchPlaceholder = "Procurar...",
    emptyMessage = "Nenhum item encontrado.",
}: SelectFieldProps & {
    name: string;
    label: string;
    showFlags?: boolean;
    flagProp?: string;
    enableSearch?: boolean;
    searchPlaceholder?: string;
    emptyMessage?: string;
    reserveErrorSpace?: boolean;
}) {
    const [open, setOpen] = useState(false)
    const commandListRef = useRef<HTMLDivElement>(null)
    
    // Hook para detectar se é mobile
    const isMobile = () => {
        return typeof window !== 'undefined' && window.innerWidth < 768
    }

    // Função para fazer scroll em toda a tela (mobile)
    useEffect(() => {
        if (!open || !isMobile()) return

        let startY = 0
        let isScrolling = false
        
        const handleTouchStart = (e: TouchEvent) => {
            startY = e.touches[0].clientY
            isScrolling = true
            // Previne scroll da página
            document.body.style.overflow = 'hidden'
        }
        
        const handleTouchMove = (e: TouchEvent) => {
            if (!isScrolling) return
            
            e.preventDefault() // Previne scroll da página
            
            const currentY = e.touches[0].clientY
            const deltaY = startY - currentY
            
            // Aplica scroll na dropdown ativa
            let activeScrollElement: HTMLElement | null = commandListRef.current

            // Se não é CommandList, procura pelo SelectContent
            if (!activeScrollElement) {
                activeScrollElement = document.querySelector('[data-radix-select-viewport]') as HTMLElement | null
            }

            if (activeScrollElement) {
                activeScrollElement.scrollTop += deltaY * 2 // Multiplicador para scroll mais responsivo
                startY = currentY
            }
        }
        
        const handleTouchEnd = () => {
            isScrolling = false
            // Restaura scroll da página
            document.body.style.overflow = ''
        }
        
        // Adiciona listeners em toda a tela
        document.addEventListener('touchstart', handleTouchStart, { passive: false })
        document.addEventListener('touchmove', handleTouchMove, { passive: false })
        document.addEventListener('touchend', handleTouchEnd, { passive: false })
        
        // Cleanup quando dropdown fechar ou componente desmontar
        return () => {
            document.removeEventListener('touchstart', handleTouchStart)
            document.removeEventListener('touchmove', handleTouchMove)
            document.removeEventListener('touchend', handleTouchEnd)
            document.body.style.overflow = '' // Garante que restore o scroll
        }
    }, [open])

    // Se search estiver habilitado, usa Popover + Command
    if (enableSearch) {
        return (
            <FormField
                control={control}
                name={name}
                render={({ field, fieldState }) => (
                    <FormItem className={cn("flex flex-col", className)}>
                        <FormLabel className="text-black text-sm font-medium mb-2">
                            {label} {required && <span className="text-red-500">*</span>}
                        </FormLabel>
                        <Popover open={open} onOpenChange={setOpen}>
                            <PopoverTrigger asChild>
                                <FormControl>
                                    <Button
                                        variant="outline"
                                        role="combobox"
                                        aria-expanded={open}
                                        className={cn(
                                            "w-full justify-between font-normal text-left h-10",
                                            "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
                                            "text-black bg-white border-gray-300 rounded-md",
                                            "focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",
                                            !field.value && "text-gray-700"
                                        )}
                                        disabled={disabled}
                                    >
                                        {field.value ? (
                                            <span className="flex items-center min-h-[20px]">
                                                {(() => {
                                                    const selectedOption = options.find((opt) => opt.value === field.value)
                                                    return selectedOption ? (
                                                        <div className="flex items-center">
                                                            {showFlags && (selectedOption as any)[flagProp] && (
                                                                <FlagImage
                                                                    countryCode={(selectedOption as any)[flagProp]}
                                                                    width={24}
                                                                    height={16}
                                                                    className="mr-2 flex-shrink-0"
                                                                    alt={`Bandeira de ${selectedOption.label}`}
                                                                />
                                                            )}
                                                            <span className="text-black truncate">{selectedOption.label}</span>
                                                        </div>
                                                    ) : (
                                                        <span className="text-black">{field.value}</span>
                                                    )
                                                })()}
                                            </span>
                                        ) : (
                                            <span className="text-gray-800">{placeholder}</span>
                                        )}
                                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 text-gray-700" />
                                    </Button>
                                </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-full p-0 bg-white border shadow-lg">
                                <Command className="bg-white">
                                    <div className="flex items-center border-b px-3 text-black">
                                        <Search className="mr-2 h-4 w-4 shrink-0 text-gray-700" />
                                        <CommandPrimitive.Input
                                            className="flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-none text-black placeholder:text-gray-700 disabled:cursor-not-allowed disabled:opacity-50"
                                            placeholder={searchPlaceholder}
                                        />
                                    </div>
                                    <CommandList 
                                        ref={commandListRef}
                                        className={cn(
                                            "max-h-[300px] overflow-auto",
                                            "touch-pan-y sm:touch-auto",
                                            "overscroll-contain"
                                        )}
                                    >
                                        <CommandEmpty className="text-black text-sm py-6 font-normal text-center w-full">
                                            {emptyMessage}
                                        </CommandEmpty>
                                        <CommandGroup>
                                            {options.map((option, index) => (
                                                <CommandItem
                                                    key={`${name}-${option.value}-${index}`}
                                                    value={option.label}
                                                    className="text-black hover:bg-gray-100 hover:text-black data-[selected=true]:bg-gray-100 data-[selected=true]:text-black cursor-pointer py-2 px-3"
                                                    onSelect={() => {
                                                        field.onChange(option.value)
                                                        setOpen(false)
                                                    }}
                                                >
                                                    <div className="flex items-center w-full">
                                                        <Check
                                                            className={cn(
                                                                "mr-2 h-4 w-4 flex-shrink-0",
                                                                option.value === field.value
                                                                    ? "opacity-100"
                                                                    : "opacity-0"
                                                            )}
                                                        />
                                                        {showFlags && (option as any)[flagProp] && (
                                                            <FlagImage
                                                                countryCode={(option as any)[flagProp]}
                                                                width={24}
                                                                height={16}
                                                                className="mr-2 flex-shrink-0"
                                                                alt={`Bandeira de ${option.label}`}
                                                            />
                                                        )}
                                                        <span className="text-black truncate">{option.label}</span>
                                                    </div>
                                                </CommandItem>
                                            ))}
                                        </CommandGroup>
                                    </CommandList>
                                </Command>
                            </PopoverContent>
                        </Popover>
                        
                        {/* Espaço reduzido para mensagem de erro */}
                        <div className="min-h-[5px]">
                            {fieldState.error && <FormMessage />}
                        </div>
                    </FormItem>
                )}
            />
        )
    }

    // Se search não estiver habilitado, usa Select normal
    return (
        <FormField
            control={control}
            name={name}
            render={({ field, fieldState }) => (
                <FormItem className={cn("flex flex-col", className)}>
                    <FormLabel className="text-black text-sm font-medium mb-2">
                        {label} {required && <span className="text-red-500">*</span>}
                    </FormLabel>
                    <Select
                        onValueChange={field.onChange}
                        value={field.value || ""}
                        disabled={disabled}
                    >
                        <FormControl>
                            <SelectTrigger className="text-black h-10 border-gray-300">
                                <SelectValue placeholder={placeholder} />
                            </SelectTrigger>
                        </FormControl>
                        <SelectContent 
                            className={cn(
                                "bg-white",
                                "touch-pan-y sm:touch-auto",
                                "overscroll-contain"
                            )}
                        >
                            {options.map((option, index) => (
                                <SelectItem
                                    key={`${name}-${option.value}-${index}`}
                                    value={option.value}
                                    disabled={option.disabled}
                                    className="text-black hover:bg-gray-100 hover:text-black focus:bg-gray-100 focus:text-black"
                                >
                                    <div className="flex items-center">
                                        {showFlags && (option as any)[flagProp] && (
                                            <FlagImage
                                                countryCode={(option as any)[flagProp]}
                                                width={24}
                                                height={16}
                                                className="mr-2"
                                                alt={`Bandeira de ${option.label}`}
                                            />
                                        )}
                                        <span className="text-black">{option.label}</span>
                                    </div>
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                    
                    {/* Espaço reduzido para mensagem de erro */}
                    <div className="min-h-[5px]">
                        {fieldState.error && <FormMessage />}
                    </div>
                </FormItem>
            )}
        />
    )
}
