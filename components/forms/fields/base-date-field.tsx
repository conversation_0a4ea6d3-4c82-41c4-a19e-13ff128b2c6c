"use client"

import { useState } from "react"
import { format, get<PERSON><PERSON>h, getYear, set<PERSON><PERSON>h, setYear } from "date-fns"
import { pt } from "date-fns/locale"
import { CalendarIcon } from "lucide-react"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { MONTHS } from "./constants"
import type { DateFieldProps } from "./types"

const generateYears = (startYear: number, endYear: number, reverse = true) => {
    const years = []
    for (let year = startYear; year <= endYear; year++) {
        years.push(year)
    }
    return reverse ? years.reverse() : years
}

export function BaseDateField({
    control,
    name,
    label,
    placeholder = "Selecione uma data",
    className = "",
    required = true,
    minDate,
    maxDate,
    disabled = false,
    clearButtonText = "Limpar",
    yearRange = { start: 1900, end: new Date().getFullYear() },
    locale = "pt",
}: DateFieldProps & {
    name: string;
    label: string;
    yearRange?: { start: number; end: number };
    locale?: "pt" | "en";
    dateFormat?: string;
    showTodayButton?: boolean;
    todayButtonText?: string;
    popoverAlign?: "start" | "end" | "center";
    reserveErrorSpace?: boolean;
}) {
    const [calendarDate, setCalendarDate] = useState(new Date())
    const [open, setOpen] = useState(false)

    const YEARS = generateYears(yearRange.start, yearRange.end)

    const generateDaysForMonth = (year: number, month: number) => {
        const firstDayOfMonth = new Date(year, month, 1)
        const lastDayOfMonth = new Date(year, month + 1, 0)
        const daysInMonth = lastDayOfMonth.getDate()

        const days = []
        const firstDayOfWeek = firstDayOfMonth.getDay()

        for (let i = 0; i < firstDayOfWeek; i++) {
            days.push(null)
        }

        for (let day = 1; day <= daysInMonth; day++) {
            days.push(new Date(year, month, day))
        }

        return days
    }

    return (
        <FormField
            control={control}
            name={name}
            render={({ field, fieldState }) => (
                <FormItem className={cn("flex flex-col", className)}>
                    <FormLabel className="text-black text-sm font-medium mb-2">
                        {label} {required && <span className="text-red-500">*</span>}
                    </FormLabel>
                    <Popover open={open} onOpenChange={setOpen}>
                        <PopoverTrigger asChild>
                            <FormControl>
                                <Button
                                    variant={"outline"}
                                    className={cn(
                                        "w-full pl-3 text-left h-10 border-gray-300",
                                        "focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",
                                        "bg-white",
                                        !field.value && "text-muted-foreground"
                                    )}
                                    disabled={disabled}
                                >
                                    {field.value ? (
                                        <span className="text-black">
                                            {(() => {
                                                try {
                                                    // Se field.value é uma string no formato DD/MM/YYYY
                                                    if (typeof field.value === 'string') {
                                                        const [day, month, year] = field.value.split('/')
                                                        const dateObj = new Date(parseInt(year), parseInt(month) - 1, parseInt(day))
                                                        return format(dateObj, "dd 'de' MMMM 'de' yyyy", { locale: pt })
                                                    }
                                                    // Se ainda é um objeto Date (fallback)
                                                    return format(field.value, "dd 'de' MMMM 'de' yyyy", { locale: pt })
                                                } catch {
                                                    return field.value
                                                }
                                            })()} 
                                        </span>
                                    ) : (
                                        <span className="text-gray-800">{placeholder}</span>
                                    )}
                                    <CalendarIcon className="ml-auto h-4 w-4 opacity-50 text-primary" />
                                </Button>
                            </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-4 bg-white" align="start">
                            <div className="space-y-4">
                                {/* Seletores de Ano e Mês */}
                                <div className="flex justify-between items-center">
                                    <div className="grid grid-cols-2 gap-2 w-full">
                                        <Select
                                            value={getMonth(calendarDate).toString()}
                                            onValueChange={(value) => {
                                                setCalendarDate(setMonth(calendarDate, Number.parseInt(value)))
                                            }}
                                        >
                                            <SelectTrigger className="h-8 text-black hover:bg-gray-100">
                                                <SelectValue>{MONTHS[getMonth(calendarDate)]}</SelectValue>
                                            </SelectTrigger>
                                            <SelectContent className="bg-white text-black">
                                                {MONTHS.map((month, index) => (
                                                    <SelectItem
                                                        key={month}
                                                        value={index.toString()}
                                                        className="text-black hover:bg-gray-100 focus:bg-gray-100 focus:text-black"
                                                    >
                                                        {month}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>

                                        <Select
                                            value={getYear(calendarDate).toString()}
                                            onValueChange={(value) => {
                                                setCalendarDate(setYear(calendarDate, Number.parseInt(value)))
                                            }}
                                        >
                                            <SelectTrigger className="h-8 text-black hover:bg-gray-100">
                                                <SelectValue>{getYear(calendarDate)}</SelectValue>
                                            </SelectTrigger>
                                            <SelectContent className="bg-white text-black">
                                                {YEARS.map((year) => (
                                                    <SelectItem
                                                        key={year}
                                                        value={year.toString()}
                                                        className="text-black hover:bg-gray-100 focus:bg-gray-100 focus:text-black"
                                                    >
                                                        {year}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>
                                </div>

                                {/* Cabeçalho dos dias da semana */}
                                <div className="grid grid-cols-7 gap-1">
                                    {(locale === "pt" ? ["D", "S", "T", "Q", "Q", "S", "S"] : ["S", "M", "T", "W", "T", "F", "S"]).map((day, index) => (
                                        <div key={index} className="h-8 w-8 flex items-center justify-center text-sm font-medium text-gray-800">
                                            {day}
                                        </div>
                                    ))}
                                </div>

                                {/* Dias do Mês */}
                                <div className="grid grid-cols-7 gap-1">
                                    {generateDaysForMonth(getYear(calendarDate), getMonth(calendarDate)).map((day, index) => {
                                        if (day === null) {
                                            return <div key={`empty-${index}`} className="h-8 w-8" />
                                        }

                                        const isSelected = (() => {
                                            if (!field.value) return false
                                            
                                            // Se field.value é uma string no formato DD/MM/YYYY
                                            if (typeof field.value === 'string') {
                                                const [dayStr, monthStr, yearStr] = field.value.split('/')
                                                return day.getDate() === parseInt(dayStr) &&
                                                       getMonth(day) === (parseInt(monthStr) - 1) &&
                                                       getYear(day) === parseInt(yearStr)
                                            }
                                            return day.getDate() === field.value.getDate() &&
                                                   getMonth(day) === getMonth(field.value) &&
                                                   getYear(day) === getYear(field.value)
                                        })()

                                        const isToday =
                                            day.getDate() === new Date().getDate() &&
                                            getMonth(day) === getMonth(new Date()) &&
                                            getYear(day) === getYear(new Date())

                                        const isFuture = day > (maxDate || new Date())
                                        const isTooOld = day < (minDate || new Date(1900, 0, 1))
                                        const isDisabled = isFuture || isTooOld

                                        return (
                                            <Button
                                                key={day.toString()}
                                                variant="ghost"
                                                size="icon"
                                                className={cn(
                                                    "h-8 w-8 p-0 font-normal text-black hover:bg-gray-100",
                                                    isSelected && "bg-primary text-primary-foreground hover:bg-primary",
                                                    isToday && !isSelected && "border border-primary",
                                                    isDisabled && "cursor-not-allowed"
                                                )}
                                                disabled={isDisabled}
                                                onClick={() => {
                                                    if (!isDisabled) {
                                                        // Converter Date para string no formato DD/MM/YYYY
                                                        const dateString = format(day, "dd/MM/yyyy")
                                                        field.onChange(dateString)
                                                        setOpen(false) // Fecha o calendário após selecionar uma data
                                                    }
                                                }}
                                            >
                                                {day.getDate()}
                                            </Button>
                                        )
                                    })}
                                </div>

                                {/* Botões de Ação */}
                                <div className="flex justify-end space-x-2 pt-2">
                                    <Button
                                        className="bg-primary text-white"
                                        variant="outline"
                                        size="sm"
                                        onClick={() => {
                                            field.onChange("")
                                        }}
                                    >
                                        {clearButtonText}
                                    </Button>
                                </div>
                            </div>
                        </PopoverContent>
                    </Popover>
                    
                    {/* Espaço reduzido para mensagem de erro */}
                    <div className="min-h-[5px]">
                        {fieldState.error && <FormMessage />}
                    </div>
                </FormItem>
            )}
        />
    )
}
