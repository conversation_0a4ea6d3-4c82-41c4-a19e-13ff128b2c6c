"use client"

import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { cn } from "@/lib/utils"
import type { Control } from "react-hook-form"
import type { FormValues } from "../registration/schema"

interface PostalCodeFieldProps {
    control: Control<FormValues>
    name: keyof FormValues
    label: string
    placeholder?: string
    className?: string
    required?: boolean
}

export function PostalCodeField({ 
    control, 
    name,
    label,
    placeholder = "Ex: 1250-123",
    className = "",
    required = true,
}: PostalCodeFieldProps) {

    const formatPostalCode = (value: string): string => {
        const numbersOnly = value.replace(/\D/g, '')

        const limited = numbersOnly.substring(0, 7)

        if (limited.length > 4) {
            return `${limited.substring(0, 4)}-${limited.substring(4)}`
        }
        
        return limited
    }

    return (
        <FormField
            control={control}
            name={name}
            render={({ field, fieldState }) => (
                <FormItem className={cn("flex flex-col", className)}>
                    <FormLabel className="text-black text-sm font-medium mb-2">
                        {label} {required && <span className="text-red-500">*</span>}
                    </FormLabel>
                    <FormControl>
                        <Input
                            type="text"
                            placeholder={placeholder}
                            maxLength={8}
                            value={field.value || ''}
                            onChange={(e) => {
                                const formatted = formatPostalCode(e.target.value)
                                field.onChange(formatted)
                            }}
                            onBlur={field.onBlur}
                            name={field.name}
                        />
                    </FormControl>
                    <div className="min-h-[5px]">
                        {fieldState.error && <FormMessage />}
                    </div>
                </FormItem>
            )}
        />
    )
}
