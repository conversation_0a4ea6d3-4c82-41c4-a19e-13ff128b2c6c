import { Button } from "@/components/ui/button"
import { Form } from "@/components/ui/form"
import { Card, CardContent } from "@/components/ui/card"
import { PersonalInfo } from "../registration/sections/personal-info"
import { VotingInfo } from "../registration/sections/voting-info"
import { AddressInfo } from "../registration/sections/address-info"
import { TermsAcceptance } from "../registration/sections/terms-acceptance"
import { Loader2 } from "lucide-react"
import { UseFormReturn } from "react-hook-form"
import { FormValues } from "../registration/schema"

interface RegistrationFormCardProps {
    form: UseFormReturn<FormValues>
    onSubmit: (values: FormValues) => void
    isSubmitting: boolean
    isDisabled: boolean
}

export function RegistrationFormCard({ 
    form, 
    onSubmit, 
    isSubmitting, 
    isDisabled 
}: RegistrationFormCardProps) {
    return (
        <Card className="w-full max-w-4xl mx-auto">
            <CardContent className="pt-6">
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <PersonalInfo control={form.control} />
                            <AddressInfo control={form.control} />
                            <VotingInfo control={form.control} />
                            <TermsAcceptance control={form.control} />
                        </div>

                        <Button
                            type="submit"
                            className="w-full size-lg"
                            style={{ backgroundColor: "#19133a", color: "white" }}
                            disabled={isDisabled}
                        >
                            {isSubmitting ? (
                                <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    Registando...
                                </>
                            ) : (
                                "Registar"
                            )}
                        </Button>

                        {/* Copyright Section */}
                        <div className="border-t border-gray-200 pt-6 mt-8">
                            <div className="text-center space-y-2">
                                <p className="text-sm text-gray-600">
                                    © {new Date().getFullYear()} Partido CHEGA. Todos os direitos reservados.
                                </p>
                                <p className="text-xs text-gray-500">
                                    Sistema de registo oficial do Partido CHEGA - Portugal
                                </p>
                                <div className="flex justify-center space-x-4 text-xs text-gray-500">
                                    <a 
                                        href="https://partidochega.pt" 
                                        target="_blank" 
                                        rel="noopener noreferrer"
                                        className="hover:text-gray-700 underline"
                                    >
                                        Site Oficial
                                    </a>
                                    <span>•</span>
                                    <a 
                                        href="https://partidochega.pt/index.php/politicaprivacidadech/" 
                                        target="_blank" 
                                        rel="noopener noreferrer"
                                        className="hover:text-gray-700 underline"
                                    >
                                        Política de Privacidade
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </Form>
            </CardContent>
        </Card>
    )
}
