"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { CheckCircle, Home } from "lucide-react"
import Image from "next/image"

export function RegistrationSuccessPage() {
    const handleGoToPortal = () => {
        window.location.href = 'https://portalchega.pt/'
    }

    return (
        <main className="min-h-screen bg-white">
            <div className="container mx-auto py-10 px-4 sm:px-6">
                {/* Header com logo */}
                <div className="flex flex-col sm:flex-row items-center justify-center sm:justify-center gap-4 mb-10">
                    <Image src="/images/logo.png" alt="Chega Logo" width={100} height={100} />
                    <h1 className="text-3xl font-bold text-center sm:text-left text-primary">
                        Registo de Membros
                    </h1>
                </div>

                {/* Card de sucesso */}
                <div className="max-w-2xl mx-auto">
                    <Card className="border-2 shadow-lg border-green-200">
                        <CardHeader className="text-center pb-6">
                            <div className="flex justify-center mb-4">
                                <div className="bg-green-100 p-4 rounded-full">
                                    <CheckCircle className="h-16 w-16 text-green-600" />
                                </div>
                            </div>
                            <CardTitle className="text-3xl font-bold text-green-700 mb-2">
                                ✅ Registo Concluído!
                            </CardTitle>
                            <CardDescription className="text-xl">
                                O seu registo foi efetuado com sucesso
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6 text-center">
                            <div className="bg-green-50 p-6 rounded-lg border border-green-200">
                                <h3 className="text-2xl font-semibold mb-4 text-green-800">
                                    🎉 Parabéns e Bem-vindo!
                                </h3>
                                <p className="text-gray-700 mb-4 leading-relaxed text-lg">
                                    O seu registo como membro do Chega foi processado com sucesso. 
                                    Agora pode aceder ao portal dos membros para explorar todas as funcionalidades disponíveis.
                                </p>
                                <p className="text-green-700 font-medium">
                                    Obrigado por se juntar a nós!
                                </p>
                            </div>

                            <div className="space-y-4">
                                <Button
                                    onClick={handleGoToPortal}
                                    size="lg"
                                    className="w-full sm:w-auto px-8 py-3 bg-primary hover:bg-primary/90 text-white font-semibold text-lg"
                                >
                                    <Home className="h-5 w-5 mr-2" />
                                    Ir para o Portal
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </main>
    )
}
