import { z } from "zod"
import parsePhoneNumber, { isValidPhoneNumber, isPossiblePhoneNumber } from 'libphonenumber-js'
import { country_code } from "@/components/forms/fields/constants"

// Criar mapeamento de código de país para ISO
const countryCodeToISO: Record<string, string> = {}
country_code.forEach(item => {
    countryCodeToISO[item.code] = item.iso.toUpperCase()
})

// Validação do NIF
function validateNif(nif: string): boolean {
    if (nif.length === 9) {
        let added = ((parseInt(nif[7]) * 2) + (parseInt(nif[6]) * 3) + (parseInt(nif[5]) * 4) + (parseInt(nif[4]) * 5) + (parseInt(nif[3]) * 6) + (parseInt(nif[2]) * 7) + (parseInt(nif[1]) * 8) + (parseInt(nif[0]) * 9));
        let mod = added % 11;
        let control;
        if (mod === 0 || mod === 1) {
            control = 0;
        } else {
            control = 11 - mod;
        }

        return parseInt(nif[8]) === control;
    } else return false;
}

// Validação se o número é possível (formato básico) usando isPossiblePhoneNumber com ISO
function validatePhoneNumberPossible(phoneNumber: string, countryCode: string): boolean {
    if (!phoneNumber || !countryCode) return false;

    try {
        const isoCode = countryCodeToISO[countryCode];

        if (isoCode) {
            // Usar código ISO se disponível
            return isPossiblePhoneNumber(phoneNumber, isoCode as any);
        } else {
            // Fallback para formato internacional completo
            const fullNumber = countryCode + phoneNumber;
            return isPossiblePhoneNumber(fullNumber);
        }
    } catch {
        return false;
    }
}

// Validação se o número é válido (existente) usando isValidPhoneNumber com ISO
function validatePhoneNumberValid(phoneNumber: string, countryCode: string): boolean {
    if (!phoneNumber || !countryCode) return false;

    try {
        const isoCode = countryCodeToISO[countryCode];

        if (isoCode) {
            // Usar código ISO se disponível
            return isValidPhoneNumber(phoneNumber, isoCode as any);
        } else {
            // Fallback para formato internacional completo
            const fullNumber = countryCode + phoneNumber;
            return isValidPhoneNumber(fullNumber);
        }
    } catch {
        return false;
    }
}

// Validação da idade mínima (14 anos)
function validateMinAge(birthdate: string): boolean {
    if (!birthdate) return false;

    try {
        const [day, month, year] = birthdate.split('/');
        if (!day || !month || !year) return false;

        const birth = new Date(
            parseInt(year),
            parseInt(month) - 1,
            parseInt(day)
        );
        if (isNaN(birth.getTime())) return false;

        const today = new Date();
        const age = today.getFullYear() - birth.getFullYear();
        const monthDiff = today.getMonth() - birth.getMonth();

        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
            return age - 1 >= 14;
        }

        return age >= 14;
    } catch {
        return false;
    }
}

export const formSchema = z.object({
    full_name: z.string().min(2, {
        message: "O nome completo deve ter pelo menos 2 caracteres.",
    }),
    email: z.string().email({
        message: "Email inválido.",
    }),
    nif: z.string()
        .length(9, { message: "O NIF deve ter 9 dígitos." })
        .regex(/^\d{9}$/, { message: "O NIF deve conter apenas números." })
        .refine(validateNif, { message: "NIF inválido." }),
    phone_number: z.string().min(1, {
        message: "O número de telefone é obrigatório.",
    }),
    birthdate: z.string()
        .min(1, { message: "A data de nascimento é obrigatória." })
        .refine(validateMinAge, { message: "Deve ter pelo menos 14 anos de idade." }),
    gender: z.string().min(1, {
        message: "Por favor selecione um género.",
    }),
    distrito: z.string().min(1, {
        message: "O distrito é obrigatório.",
    }),
    concelho: z.string().min(1, {
        message: "O concelho é obrigatório.",
    }),
    freguesia: z.string().min(1, {
        message: "A freguesia é obrigatória.",
    }),
    country_code: z.string().min(1, {
        message: "O código do país é obrigatório.",
    }),
    country: z.string().min(1, {
        message: "O país é obrigatório.",
    }),
    address: z.string().min(5, {
        message: "A morada deve ter pelo menos 5 caracteres.",
    }),
    postal_code: z.string()
        .min(1, {
            message: "O código postal é obrigatório."
        }),
    locality: z.string().min(1, {
        message: "A localidade é obrigatória.",
    }),
}).refine(
    // Primeira validação: verifica se o formato é possível
    (data) => validatePhoneNumberPossible(data.phone_number, data.country_code),
    {
        message: "Formato de número inválido. Verifique se inseriu todos os dígitos corretamente.",
        path: ["phone_number"],
    }
).refine(
    // Segunda validação: verifica se o número é válido/existente
    (data) => validatePhoneNumberValid(data.phone_number, data.country_code),
    {
        message: "Número de telemóvel não existe.",
        path: ["phone_number"],
    }
)

export type FormValues = z.infer<typeof formSchema>
