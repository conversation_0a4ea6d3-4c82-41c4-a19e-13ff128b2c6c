import { z } from "zod"
import {isValidPhoneNumber} from 'libphonenumber-js/mobile'
import { country_code } from "@/components/forms/fields/constants"


const isoToCountryCode: Record<string, string> = {}
country_code.forEach(item => {
    isoToCountryCode[item.iso.toUpperCase()] = item.code
    isoToCountryCode[item.iso.toUpperCase()] = item.code
})

function validatePhoneWithCountry(phoneNumber: string, countryISO: string): boolean {
    if (!phoneNumber || !countryISO) return false

    const countryCode = isoToCountryCode[countryISO.toUpperCase()]
    if (!countryCode) return false
    return isValidPhoneNumber(phoneNumber, countryISO.toUpperCase() as any)
}

// Validação para garantir pelo menos dois nomes com mínimo 2 caracteres cada
function validateFullName(fullName: string): boolean {
    if (!fullName) return false
    
    // Dividir o nome por espaços e filtrar strings vazias
    const names = fullName.trim().split(/\s+/).filter(name => name.length > 0)
    
    // Verificar se tem pelo menos 2 nomes
    if (names.length < 2) return false
    
    // Verificar se cada nome tem pelo menos 2 caracteres
    return names.every(name => name.length >= 2)
}

// Validação do NIF
function validateNif(nif: string): boolean {
    if (nif.length === 9) {
        const added = ((parseInt(nif[7]) * 2) + (parseInt(nif[6]) * 3) + (parseInt(nif[5]) * 4) + (parseInt(nif[4]) * 5) + (parseInt(nif[3]) * 6) + (parseInt(nif[2]) * 7) + (parseInt(nif[1]) * 8) + (parseInt(nif[0]) * 9));
        const mod = added % 11;
        let control;
        if (mod === 0 || mod === 1) {
            control = 0;
        } else {
            control = 11 - mod;
        }

        return parseInt(nif[8]) === control;
    } else return false;
}

// Validação da idade mínima (14 anos)
function validateMinAge(birthdate: string): boolean {
    if (!birthdate) return false;

    try {
        const [day, month, year] = birthdate.split('/');
        if (!day || !month || !year) return false;

        const birth = new Date(
            parseInt(year),
            parseInt(month) - 1,
            parseInt(day)
        );
        if (isNaN(birth.getTime())) return false;

        const today = new Date();
        const age = today.getFullYear() - birth.getFullYear();
        const monthDiff = today.getMonth() - birth.getMonth();

        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
            return age - 1 >= 14;
        }

        return age >= 14;
    } catch {
        return false;
    }
}

export const formSchema = z.object({
    full_name: z.string()
        .min(2, { message: "O nome completo deve ter pelo menos 2 caracteres." })
        .refine(validateFullName, { 
            message: "Deve inserir pelo menos dois nomes (nome e apelido)."
        }),
    email: z.string().email({
        message: "Email inválido.",
    }),
    nif: z.string()
        .length(9, { message: "O NIF deve ter 9 dígitos." })
        .regex(/^\d{9}$/, { message: "O NIF deve conter apenas números." })
        .refine(validateNif, { message: "NIF inválido." }),
    phone_number: z.string()
        .min(1, { message: "O número de telefone é obrigatório." }),
    birthdate: z.string()
        .min(1, { message: "A data de nascimento é obrigatória." })
        .refine(validateMinAge, { message: "Deve ter pelo menos 14 anos de idade." }),
    gender: z.string().min(1, {
        message: "Por favor selecione um género.",
    }),
    distrito: z.string().min(1, {
        message: "O distrito é obrigatório.",
    }),
    concelho: z.string().min(1, {
        message: "O concelho é obrigatório.",
    }),
    freguesia: z.string().min(1, {
        message: "A freguesia é obrigatória.",
    }),
    country_code: z.string().min(1, {
        message: "O código do país é obrigatório.",
    }),
    country: z.string().min(1, {
        message: "O país é obrigatório.",
    }),
    address: z.string().min(5, {
        message: "A morada deve ter pelo menos 5 caracteres.",
    }),
    postal_code: z.string()
        .min(1, {
            message: "O código postal é obrigatório."
        }),
    locality: z.string().min(1, {
        message: "A localidade é obrigatória.",
    }),
    terms_accepted: z.boolean().refine(val => val === true, {
        message: "Deve aceitar os termos e condições para prosseguir.",
    }),
}).superRefine((data, ctx) => {
    if (data.phone_number && data.country_code) {
        const isValid = validatePhoneWithCountry(data.phone_number, data.country_code)
        if (!isValid) {
            ctx.addIssue({
                code: z.ZodIssueCode.custom,
                path: ["phone_number"],
                message: "Número de telefone inválido para o país selecionado."
            })
        }
    }
})

export type FormValues = z.infer<typeof formSchema>
