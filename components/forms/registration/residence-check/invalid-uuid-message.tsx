"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { AlertTriangle, Home, Mail, Phone } from "lucide-react"

export function InvalidUuidMessage() {
    const handleGoHome = () => {
        window.location.href = 'https://portalchega.pt/'
    }

    return (
        <div className="max-w-2xl mx-auto">
            <Card className="border-2 shadow-lg border-red-200">
                <CardHeader className="text-center pb-6">
                    <div className="flex justify-center mb-4">
                        <div className="bg-red-100 p-4 rounded-full">
                            <AlertTriangle className="h-12 w-12 text-red-600" />
                        </div>
                    </div>
                    <CardTitle className="text-2xl font-bold text-red-700 mb-2">
                        Link de Registo Inválido
                    </CardTitle>
                    <CardDescription className="text-lg">
                        O link que utilizou não é válido ou expirou
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6 text-center">
                    <div className="bg-red-50 p-6 rounded-lg border border-red-200">
                        <h3 className="text-xl font-semibold mb-4 text-red-800">
                            ⚠️ Não foi possível validar o registo
                        </h3>
                        <p className="text-gray-700 mb-4 leading-relaxed">
                            O identificador de registo (UUID) não foi encontrado ou é inválido. 
                            Isto pode acontecer se:
                        </p>
                        <ul className="text-left text-gray-600 text-sm space-y-2 mb-4">
                            <li>• O link expirou</li>
                            <li>• O link foi alterado acidentalmente</li>
                            <li>• Já utilizou este link anteriormente</li>
                            <li>• Houve um erro na geração do convite</li>
                        </ul>
                    </div>
                    
                    <div className="space-y-4">
                        <div className="bg-gray-50 p-4 rounded-lg mb-4">
                            <p className="font-semibold text-gray-800 mb-3">
                                💬 Contacte o Suporte:
                            </p>
                            <div className="space-y-2 text-sm text-gray-600">
                                <div className="flex items-center justify-center gap-2">
                                    <Mail className="h-4 w-4" />
                                    <span><EMAIL></span>
                                </div>
                            </div>
                        </div>
                        
                        <Button
                            onClick={handleGoHome}
                            size="lg"
                            className="font-semibold"
                        >
                            <Home className="h-4 w-4 mr-2" />
                            Ir para o Portal Chega
                        </Button>
                    </div>
                </CardContent>
            </Card>
        </div>
    )
}
