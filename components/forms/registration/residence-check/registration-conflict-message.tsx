"use client"

import {<PERSON>, CardContent, CardDescription, Card<PERSON><PERSON><PERSON>, CardTitle} from "@/components/ui/card"
import {AlertTriangle, Mail} from "lucide-react"

export function RegistrationConflictMessage() {
    return (
        <div className="max-w-2xl mx-auto">
            <Card className="border-2 shadow-lg border-orange-200">
                <CardHeader className="text-center pb-6">
                    <div className="flex justify-center mb-4">
                        <div className="bg-orange-100 p-4 rounded-full">
                            <AlertTriangle className="h-16 w-16 text-orange-600" />
                        </div>
                    </div>
                    <CardTitle className="text-3xl font-bold text-orange-700 mb-2">
                        ⚠️ Dados Já Registados
                    </CardTitle>
                    <CardDescription className="text-xl">
                        Alguns dos dados inseridos já se encontram no sistema
                    </CardDescription>
                </CardHeader>
                
                <CardContent className="space-y-6">
                    <div className="bg-orange-50 p-6 rounded-lg border border-orange-200">
                        <h3 className="text-xl font-semibold mb-4 text-orange-800 text-center">
                            Alguns dos dados inseridos já se encontram no sistema
                        </h3>
                    </div>

                    <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
                        <h3 className="text-xl font-semibold mb-4 text-blue-800 text-center">
                            📞 Como Resolver
                        </h3>
                        <p className="text-blue-700 mb-4 text-center">
                            Para resolver esta situação e completar o seu registo, 
                            entre em contacto com o nosso suporte técnico:
                        </p>
                        <div className="space-y-3 text-blue-700">
                            <div className="flex items-center justify-center gap-3 p-3 bg-white rounded-lg">
                                <Mail className="h-5 w-5 flex-shrink-0" />
                                <span className="font-medium"></span>
                            </div>
                        </div><EMAIL>
                    </div>
                </CardContent>
            </Card>
        </div>
    )
}
