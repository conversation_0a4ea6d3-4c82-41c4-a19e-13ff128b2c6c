"use client"

import { <PERSON>, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Construction } from "lucide-react"

interface NonResidentMessageProps {
    onGoBack: () => void
}

export function NonResidentMessage({ onGoBack }: NonResidentMessageProps) {
    return (
        <div className="max-w-2xl mx-auto">
            <Card className="border-2 shadow-lg">
                <CardHeader className="text-center pb-6">
                    <div className="flex justify-center mb-4">
                        <div className="bg-orange-100 p-4 rounded-full">
                            <Construction className="h-12 w-12 text-orange-600" />
                        </div>
                    </div>
                    <CardTitle className="text-2xl font-bold text-orange-700 mb-2">
                        Página em Desenvolvimento
                    </CardTitle>
                    <CardDescription className="text-lg">
                        Registo para não residentes em Portugal
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6 text-center">
                    <div className="bg-orange-50 p-6 rounded-lg border border-orange-200">
                        <h3 className="text-xl font-semibold mb-4 text-orange-800">
                            🚧 Funcionalidade em Construção
                        </h3>
                        <p className="text-gray-700 mb-4 leading-relaxed">
                            O processo de registo para cidadãos não residentes em Portugal
                            está atualmente em desenvolvimento.
                        </p>
                        <p className="text-gray-600 text-sm">
                            Esta funcionalidade estará disponível em breve. Pedimos desculpa pela inconveniência.
                        </p>
                    </div>

                    <div className="space-y-4">
                        <div className="text-sm text-gray-600">
                            <p className="mb-2">
                                <strong>Para mais informações, contacte:</strong>
                            </p>
                            <p>📧 <EMAIL></p>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    )
}
