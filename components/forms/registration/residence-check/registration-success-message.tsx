"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { CheckCircle, Home} from "lucide-react"


export function RegistrationSuccessMessage() {
    const handleGoToPortal = () => {
        window.location.href = 'https://portalchega.pt/'
    }

    return (
        <div className="max-w-2xl mx-auto">
            <Card className="border-2 shadow-lg border-green-200">
                <CardHeader className="text-center pb-6">
                    <div className="flex justify-center mb-4">
                        <div className="bg-green-100 p-4 rounded-full">
                            <CheckCircle className="h-16 w-16 text-green-600" />
                        </div>
                    </div>
                    <CardTitle className="text-3xl font-bold text-green-700 mb-2">
                         Registo Concluído!
                    </CardTitle>
                    <CardDescription className="text-xl">
                        O seu registo foi efetuado com sucesso
                    </CardDescription>
                </CardHeader>
                
                <CardContent className="space-y-6 text-center">
                    <div className="bg-green-50 p-6 rounded-lg border border-green-200">
                        <h3 className="text-2xl font-semibold mb-4 text-green-800">
                            Bem-vindo ao Partido Chega!
                        </h3>
                        <p className="text-gray-700 mb-4 leading-relaxed text-lg">
                            O seu registo como membro foi processado com sucesso.
                        </p>
                        <p className="text-gray-600 mb-4">
                            Pode agora aceder ao portal dos membros para explorar todas as funcionalidades disponíveis 
                            e participar ativamente na vida do partido.
                        </p>
                    </div>
                    
                    <div className="space-y-4">
                        <Button
                            onClick={handleGoToPortal}
                            size="lg"
                            className="w-full sm:w-auto bg-primary hover:bg-primary/90 text-white font-semibold text-lg px-8 py-3"
                        >
                            <Home className="h-5 w-5 mr-2" />
                            Fazer Login no Portal
                        </Button>
                    </div>

                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-200 mt-6">
                        <p className="text-sm text-blue-700">
                            <strong>Próximos passos:</strong> Use os dados enviados por email para fazer login no portal.
                        </p>
                    </div>
                </CardContent>
            </Card>
        </div>
    )
}
