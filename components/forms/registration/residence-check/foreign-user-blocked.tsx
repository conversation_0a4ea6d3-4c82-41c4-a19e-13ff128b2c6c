"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Globe, AlertTriangle, Mail, Home } from "lucide-react"

interface ForeignUserBlockedProps {
    onGoBack: () => void
}

export function ForeignUserBlocked({ onGoBack }: ForeignUserBlockedProps) {
    const handleGoHome = () => {
        window.location.href = 'https://portalchega.pt/'
    }

    return (
        <div className="max-w-2xl mx-auto">
            <Card className="border-2 shadow-lg border-orange-200">
                <CardHeader className="text-center pb-6">
                    <div className="flex justify-center mb-4">
                        <div className="bg-orange-100 p-4 rounded-full">
                            <Globe className="h-12 w-12 text-orange-600" />
                        </div>
                    </div>
                    <CardTitle className="text-2xl font-bold text-orange-700 mb-2">
                        Registo Estrangeiro Detectado
                    </CardTitle>
                    <CardDescription className="text-lg">
                        Este UUID já está registado como cidadão no estrangeiro
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                    <div className="bg-orange-50 p-6 rounded-lg border border-orange-200">
                        <div className="flex items-center gap-3 mb-4">
                            <AlertTriangle className="h-6 w-6 text-orange-600" />
                            <span className="font-semibold text-orange-800 text-lg">
                                Acesso Restrito
                            </span>
                        </div>
                        <p className="text-gray-700 mb-4 leading-relaxed">
                            O seu UUID foi previamente registado como cidadão português no estrangeiro. 
                            Por este motivo, não pode aceder ao formulário de registo nacional.
                        </p>
                        <div className="bg-white p-4 rounded border border-orange-200">
                            <h4 className="font-semibold text-orange-800 mb-2">
                                ℹ️ Informação Importante
                            </h4>
                            <p className="text-sm text-gray-700 leading-relaxed">
                                Se considera que isto é um erro ou se a sua situação de residência mudou, 
                                por favor contacte o nosso suporte para assistência.
                            </p>
                        </div>
                    </div>
                    
                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                        <h3 className="font-semibold text-blue-800 mb-2">
                            📞 Precisa de Ajuda?
                        </h3>
                        <p className="text-gray-700 text-sm leading-relaxed mb-3">
                            Se tem dúvidas sobre o seu registo ou precisa de alterar a sua situação, 
                            contacte-nos através dos canais abaixo:
                        </p>
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                            <Mail className="h-4 w-4" />
                            <span><EMAIL></span>
                        </div>
                    </div>

                    <div className="space-y-4">
                        <div className="flex flex-col sm:flex-row gap-3">
                            <Button 
                                onClick={onGoBack}
                                variant="outline"
                                className="flex-1"
                                size="lg"
                            >
                                Voltar à Verificação
                            </Button>
                            
                            <Button 
                                onClick={handleGoHome}
                                className="flex-1"
                                size="lg"
                            >
                                <Home className="h-4 w-4 mr-2" />
                                Ir para o Portal
                            </Button>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    )
}
