"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Globe, CheckCircle } from "lucide-react"

interface ForeignRegistrationValidProps {
    identifier: string
    onGoBack: () => void
}

export function ForeignRegistrationValid({ identifier, onGoBack }: ForeignRegistrationValidProps) {
    return (
        <div className="max-w-2xl mx-auto">
            <Card className="border-2 shadow-lg border-green-200">
                <CardHeader className="text-center pb-6">
                    <div className="flex justify-center mb-4">
                        <div className="bg-green-100 p-4 rounded-full">
                            <Globe className="h-12 w-12 text-green-600" />
                        </div>
                    </div>
                    <CardTitle className="text-2xl font-bold text-green-700 mb-2">
                        Registo no Estrangeiro Confirmado
                    </CardTitle>
                    <CardDescription className="text-lg">
                        O seu UUID foi registado com sucesso
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                    <div className="bg-green-50 p-6 rounded-lg border border-green-200">
                        <div className="flex items-center gap-3 mb-4">
                            <CheckCircle className="h-6 w-6 text-green-600" />
                            <span className="font-semibold text-green-800 text-lg">
                                Registo Confirmado
                            </span>
                        </div>
                        <p className="text-gray-700 mb-4 leading-relaxed">
                            O seu UUID foi registado com sucesso como cidadão português no estrangeiro.
                        </p>
                        <div className="bg-white p-3 rounded border">
                            <p className="text-sm text-gray-600 mb-1">UUID de Registo:</p>
                            <p className="font-mono text-sm text-gray-800 break-all">{identifier}</p>
                        </div>
                    </div>
                    
                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                        <h3 className="font-semibold text-blue-800 mb-2">
                            📋 Próximos Passos
                        </h3>
                        <p className="text-gray-700 text-sm leading-relaxed">
                            O processo de registo para cidadãos no estrangeiro está em desenvolvimento. 
                            Será contactado em breve com as instruções específicas para completar o seu registo.
                        </p>
                    </div>

                    <div className="space-y-4">
                        <div className="text-center text-sm text-gray-600">
                            <p className="mb-2">
                                <strong>Para mais informações:</strong>
                            </p>
                            <p>📧 <EMAIL></p>
                        </div>
                        
                        <Button 
                            onClick={onGoBack}
                            variant="outline"
                            className="w-full"
                            size="lg"
                        >
                            Voltar à Verificação de Residência
                        </Button>
                    </div>
                </CardContent>
            </Card>
        </div>
    )
}
