"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { AlertTriangle, Home, Mail} from "lucide-react"

export function ConflictErrorMessage() {
    const handleGoToPortal = () => {
        window.location.href = 'https://portalchega.pt/'
    }
    return (
        <div className="max-w-2xl mx-auto">
            <Card className="border-2 shadow-lg border-orange-200">
                <CardHeader className="text-center pb-6">
                    <div className="flex justify-center mb-4">
                        <div className="bg-orange-100 p-4 rounded-full">
                            <AlertTriangle className="h-12 w-12 text-orange-600" />
                        </div>
                    </div>
                    <CardTitle className="text-2xl font-bold text-orange-700 mb-2">
                        Registo Já E<PERSON>ado
                    </CardTitle>
                    <CardDescription className="text-lg">
                        Este convite já foi utilizado anteriormente
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6 text-center">
                    <div className="bg-orange-50 p-6 rounded-lg border border-orange-200">
                        <h3 className="text-xl font-semibold mb-4 text-orange-800">
                            ✅ Já é membro registado
                        </h3>
                        <p className="text-gray-700 mb-4 leading-relaxed">
                            O link de registo que utilizou já foi processado com sucesso. 
                            Se já completou o seu registo, pode fazer login na sua conta.
                        </p>
                        <p className="text-gray-600 text-sm mb-4">
                            Se não se lembra de ter completado o registo ou tem dúvidas sobre a sua conta, 
                            contacte o nosso suporte.
                        </p>
                    </div>
                    
                    <div className="space-y-4">
                        <div className="bg-gray-50 p-4 rounded-lg">
                            <p className="font-semibold text-gray-800 mb-3">
                                💬 Contacte o Suporte:
                            </p>
                            <div className="space-y-2 text-sm text-gray-600">
                                <div className="flex items-center justify-center gap-2">
                                    <Mail className="h-4 w-4" />
                                    <a href="mailto:<EMAIL>" className="font-medium text-blue-700"><EMAIL></a>
                                </div>
                            </div>
                        </div>

                        <div className="flex flex-col sm:flex-row gap-3 justify-center">
                            <Button
                                onClick={handleGoToPortal}
                                variant="outline"
                                size="lg"
                                className="font-semibold bg-primary text-white hover:bg-primary/90"
                            >
                                <Home className="h-4 w-4 mr-2" />
                                Ir para o Portal
                            </Button>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    )
}
