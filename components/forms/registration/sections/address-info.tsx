"use client"

import type {Control} from "react-hook-form"
import {
    BaseInputField,
    PostalCodeField
} from "@/components/forms/fields"
import type {FormValues} from "../schema"

interface AddressInfoProps {
    control: Control<FormValues>
}

export function AddressInfo({ control }: AddressInfoProps) {
    return (
        <div className="space-y-4 md:col-span-2">
            <h2 className="text-xl font-semibold" style={{color: "#19133a"}}>
                Informações de Morada
            </h2>
            
            <BaseInputField
                control={control}
                name="address"
                label="Morada"
                placeholder="Ex: Rua da Liberdade, 123, 2º Esq"
                required
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <PostalCodeField
                    control={control}
                    name="postal_code"
                    label="Código Postal"
                    placeholder="Ex: 1250-123"
                    required
                />
                
                <BaseInputField
                    control={control}
                    name="locality"
                    label="Localidade"
                    placeholder="Ex: Lisboa"
                    required
                />
            </div>
        </div>
    )
}
