"use client"

import { useEffect } from "react"
import type { Control } from "react-hook-form"
import { useWatch, useFormContext } from "react-hook-form"
import { BaseSelectField } from "@/components/forms/fields"
import type { FormValues } from "../schema"
import { useGetDistricts } from "@/utils/queries/district"
import { useGetMunicipalities } from "@/utils/queries/municipality"
import { useGetParishes } from "@/utils/queries/parish"

interface VotingInfoProps {
    control: Control<FormValues>
}

const extractArray = (data: any): any[] => {
    if (Array.isArray(data)) return data
    if (data?.results) return data.results
    if (data?.data) return data.data
    return []
}

const mapToOptions = (items: any[]) => 
    items.map(item => ({
        value: item.code || item.id || item.name,
        label: item.name || item.label || item.code || item
    }))

export function VotingInfo({ control }: VotingInfoProps) {
    const { setValue } = useFormContext<FormValues>()

    const selectedDistrict = useWatch({ control, name: "distrito" })
    const selectedMunicipality = useWatch({ control, name: "concelho" })

    const { data: districts, isLoading: loadingDistricts } = useGetDistricts()
    const { data: municipalities, isLoading: loadingMunicipalities } = useGetMunicipalities(undefined, selectedDistrict)
    const { data: parishes, isLoading: loadingParishes } = useGetParishes(undefined, selectedMunicipality)

    const districtOptions = mapToOptions(extractArray(districts))
    const municipalityOptions = mapToOptions(extractArray(municipalities))
    const parishOptions = mapToOptions(extractArray(parishes))

    // Reset concelho e freguesia quando distrito muda
    useEffect(() => {
        setValue("concelho", "")
        setValue("freguesia", "")
    }, [selectedDistrict, setValue])

    // Reset freguesia quando concelho muda  
    useEffect(() => {
        setValue("freguesia", "")
    }, [selectedMunicipality, setValue])

    return (
        <div className="space-y-4 md:col-span-2">
            <h2 className="text-xl font-semibold" style={{ color: "#19133a" }}>
                Local de Voto
            </h2>

            <BaseSelectField
                control={control}
                name="distrito"
                label="Distrito"
                placeholder={loadingDistricts ? "Carregando distritos..." : "Selecione um distrito"}
                options={districtOptions}
                required
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <BaseSelectField
                    control={control}
                    name="concelho"
                    label="Concelho"
                    placeholder={
                        !selectedDistrict
                            ? "Selecione primeiro um distrito"
                            : loadingMunicipalities
                                ? "Carregando concelhos..."
                                : "Selecione um concelho"
                    }
                    options={municipalityOptions}
                    disabled={!selectedDistrict || loadingMunicipalities}
                    required
                />
                
                <BaseSelectField
                    control={control}
                    name="freguesia"
                    label="Freguesia"
                    placeholder={
                        !selectedMunicipality
                            ? "Selecione primeiro um concelho"
                            : loadingParishes
                                ? "Carregando freguesias..."
                                : "Selecione uma freguesia"
                    }
                    options={parishOptions}
                    disabled={!selectedMunicipality || loadingParishes}
                    required
                />
            </div>
        </div>
    )
}
