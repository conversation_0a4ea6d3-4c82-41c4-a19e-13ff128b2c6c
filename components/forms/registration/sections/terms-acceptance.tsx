"use client"

import type { Control } from "react-hook-form"
import { BaseCheckboxField } from "@/components/forms/fields"
import type { FormValues } from "../schema"

interface TermsAcceptanceProps {
    control: Control<FormValues>
}

export function TermsAcceptance({ control }: TermsAcceptanceProps) {
    return (
        <div className="space-y-4 md:col-span-2">
            <BaseCheckboxField
                control={control}
                name="terms_accepted"
                label={
                    <>
                        Li e aceito os{" "}
                        <a 
                            href="https://partidochega.pt/index.php/politicaprivacidadech/" 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:text-blue-800 underline"
                        >
                            termos e condições
                        </a>
                        {" "}e política de privacidade
                    </>
                }
                required
                className="rounded-lg border border-gray-200 p-4 bg-gray-50/50"
            />
        </div>
    )
}
