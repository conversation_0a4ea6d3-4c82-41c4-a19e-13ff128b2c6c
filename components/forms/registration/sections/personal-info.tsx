"use client"

import type { Control } from "react-hook-form"
import {
    BaseInputField,
    BaseSelectField,
    BaseDateField,
    country_code
} from "@/components/forms/fields"
import type {FormValues} from "../schema"

interface PersonalInfoProps {
    control: Control<FormValues>
}

const genderOptions = [
    { value: "male", label: "Masculino" },
    { value: "female", label: "Feminino" }
]

const countryCodeOptions = country_code.map(country => ({
    value: country.iso,
    label: `${country.code} (${country.country})`,
    iso: country.iso,
}))

export function PersonalInfo({control}: PersonalInfoProps) {
    return (
        <div className="space-y-4 md:col-span-2">
            <h2 className="text-xl font-semibold" style={{color: "#19133a"}}>
                Informações Pessoais
            </h2>

            <BaseInputField
                control={control}
                name="full_name"
                label="Nome Completo"
                placeholder="Ex: <PERSON>"
                required
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <BaseInputField
                    control={control}
                    name="email"
                    label="Email (não editável)"
                    placeholder="Ex: <EMAIL>"
                    type="email"
                    disabled={true}
                    required
                />

                <BaseInputField
                    control={control}
                    name="nif"
                    label="NIF"
                    placeholder="Ex: 123456789"
                    maxLength={9}
                    required
                />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-12 gap-4">
                <div className="col-span-1 md:col-span-4">
                    <BaseSelectField
                        control={control}
                        name="country_code"
                        label="Código do País"
                        placeholder="Selecione"
                        options={countryCodeOptions}
                        showFlags={true}
                        flagProp="iso"
                        enableSearch={true}
                        searchPlaceholder="Procurar país..."
                        emptyMessage="Nenhum país encontrado."
                        required
                    />
                </div>

                <div className="col-span-1 md:col-span-8">
                    <BaseInputField
                        control={control}
                        name="phone_number"
                        label="Número de Telefone"
                        placeholder="Digite seu número de telefone"
                        type="tel"
                        required
                    />
                </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <BaseDateField
                    control={control}
                    name="birthdate"
                    label="Data de Nascimento"
                    placeholder="Selecione sua data de nascimento"
                    minDate={new Date(1900, 0, 1)}
                    maxDate={new Date()}
                    yearRange={{ start: 1900, end: new Date().getFullYear() }}
                    locale="pt"
                    dateFormat="dd/MM/yyyy"
                    required
                />

                <BaseSelectField
                    control={control}
                    name="gender"
                    label="Sexo"
                    placeholder="Selecione o seu sexo..."
                    options={genderOptions}
                    required
                />
            </div>
        </div>
    )
}
