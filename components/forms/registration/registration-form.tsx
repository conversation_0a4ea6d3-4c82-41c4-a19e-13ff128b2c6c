"use client"

import {zodResolver} from "@hookform/resolvers/zod"
import {useForm} from "react-hook-form"
import {useEffect, useState} from "react"
import {useSearchParams} from "next/navigation"
import {toast} from "@/hooks/use-toast"
import {formSchema, type FormValues} from "./schema"
import {useRegisterUser, useValidateRegistration} from "@/utils/queries/register"
import {RegisterCredentials} from "@/utils/types/user"
import {country_code} from "@/components/forms/fields/constants"
import {LoadingCard, RegistrationFormCard} from "../fields"
import {
    ConflictErrorMessage,
    InvalidUuidMessage,
    NonResidentMessage,
    RegistrationConflictMessage,
    RegistrationSuccessMessage,
    ResidenceCheck
} from "./residence-check"
import {useRecaptcha} from "@/utils/recaptcha"

export default function RegistrationForm() {
    const searchParams = useSearchParams()
    const {executeRecaptcha} = useRecaptcha()
    const uuid = searchParams.get('uuid') || searchParams.get('identifier') || ''

    // Função para transformar ISO em country code
    const getCountryCodeFromISO = (iso: string): string => {
        const country = country_code.find(item => item.iso.toLowerCase() === iso.toLowerCase())
        return country?.code || '+351' // Default para Portugal
    }

    // Função para processar número de telefone
    const processPhoneNumber = (phoneNumber: string): string => {
        if (!phoneNumber) return ''

        // Se começa com código de país (ex: "pt925062793")
        const isoMatch = phoneNumber.match(/^([a-z]{2})(\d+)$/i)
        if (isoMatch) {
            const [, number] = isoMatch
            return number // Retornar apenas o número sem prefixo
        }

        // Se começa com + remover
        if (phoneNumber.startsWith('+')) {
            return phoneNumber.replace(/^\+\d{1,4}/, '') // Remove código do país
        }

        // Remover espaços e caracteres especiais, manter apenas números
        return phoneNumber.replace(/\D/g, '')
    }


    const [residenceChecked, setResidenceChecked] = useState(false)
    const [isPortugalResident, setIsPortugalResident] = useState<boolean | null>(null)
    const [showSuccessMessage, setShowSuccessMessage] = useState(false)
    const [showConflictMessage, setShowConflictMessage] = useState(false)

    const validation = useValidateRegistration(uuid, isPortugalResident === false ? true : undefined)
    const registerMutation = useRegisterUser()

    const form = useForm<FormValues>({
        resolver: zodResolver(formSchema),
        mode: "onChange", // Validação em tempo real ao digitar
        reValidateMode: "onChange", // Revalidação em tempo real
        defaultValues: {
            full_name: "",
            email: "",
            nif: "",
            phone_number: "",
            birthdate: "",
            gender: "",
            distrito: "",
            concelho: "",
            freguesia: "",
            country_code: "pt",
            country: "Portugal",
            address: "",
            postal_code: "",
            locality: "",
            terms_accepted: false,
        },
    })

    useEffect(() => {
        if (validation.isSuccess && validation.data?.data?.email) {
            form.setValue('email', validation.data.data.email, {
                shouldDirty: true,
                shouldTouch: true
            })
        }
    }, [validation.isSuccess, validation.data, form])

    const mapFormDataToAPI = (formData: FormValues): RegisterCredentials => {
        const cleanPhoneNumber = processPhoneNumber(formData.phone_number || '')
        let countryCode = formData.country_code || '+351'
        if (countryCode.length === 2 && !countryCode.startsWith('+')) {
            countryCode = getCountryCodeFromISO(countryCode)
        }

        return {
            full_name: formData.full_name || '',
            registration_id: uuid || '',
            nif: formData.nif || '',
            email: formData.email || '',
            phone_number: cleanPhoneNumber,
            birthdate: formData.birthdate || '',
            gender: formData.gender || '',
            district: formData.distrito || '',
            municipality: formData.concelho || '',
            parish: formData.freguesia || '',
            country_code: countryCode,
            country: formData.country || 'Portugal',
            address: formData.address || '',
            postal_code: formData.postal_code || '',
            locality: formData.locality || '',
            recaptcha_token: ''
        }
    }

    const onSubmit = async (values: FormValues) => {
        const recaptchaToken = await executeRecaptcha()
        const apiData = mapFormDataToAPI(values)
        apiData['recaptcha_token'] = recaptchaToken

        registerMutation.mutate(apiData, {
            onSuccess: () => {
                form.reset()
                setShowSuccessMessage(true)
            },
            onError: (error: any) => {
                // Verificar se é erro 409 (conflito/dados duplicados)
                if (error?.response?.status === 409) {
                    setShowConflictMessage(true)
                } else {
                    // Para outros erros, mostrar toast
                    const errorMessage = error?.response?.data?.message ||
                        error?.message ||
                        "Ocorreu um erro no registo. Tente novamente."

                    toast({
                        title: "❌ Erro no registo",
                        description: errorMessage,
                        variant: "destructive"
                    })
                }
            }
        })
    }

    const handleResidenceConfirmed = (isResident: boolean) => {
        setIsPortugalResident(isResident)
        setResidenceChecked(true)
    }

    // Se deve mostrar mensagem de sucesso
    if (showSuccessMessage) {
        return <RegistrationSuccessMessage/>
    }

    // Se deve mostrar mensagem de conflito
    if (showConflictMessage) {
        return <RegistrationConflictMessage/>
    }

    if (validation.isLoading) {
        return <LoadingCard/>
    }

    if (validation.isConflictError) {
        return <ConflictErrorMessage/>
    }

    if (!uuid || (!validation.isSuccess && !validation.isConflictError)) {
        return <InvalidUuidMessage/>
    }

    if (validation.isSuccess && validation.isForeignUser) {
        return <NonResidentMessage/>
    }

    if (!residenceChecked) {
        return <ResidenceCheck onResidenceConfirmed={handleResidenceConfirmed}/>
    }

    if (isPortugalResident === false) {
        return <NonResidentMessage/>
    }

    if (!validation.isUserRegistered && !validation.isConflictError) {
        return (
            <RegistrationFormCard
                form={form}
                onSubmit={onSubmit}
                isSubmitting={registerMutation.isPending}
                isDisabled={registerMutation.isPending || validation.isLoading || uuid === ''}
            />
        )
    }

    return null
}
