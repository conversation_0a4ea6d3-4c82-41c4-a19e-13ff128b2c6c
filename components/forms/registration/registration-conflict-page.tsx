"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { AlertTriangle, Mail, Phone, Home } from "lucide-react"
import Image from "next/image"

export function RegistrationConflictPage() {
    const handleGoToPortal = () => {
        window.location.href = 'https://portalchega.pt/'
    }

    const handleTryAgain = () => {
        window.location.reload()
    }

    return (
        <main className="min-h-screen bg-white">
            <div className="container mx-auto py-10 px-4 sm:px-6">
                {/* Header com logo */}
                <div className="flex flex-col sm:flex-row items-center justify-center sm:justify-center gap-4 mb-10">
                    <Image src="/images/logo.png" alt="Chega Logo" width={100} height={100} />
                    <h1 className="text-3xl font-bold text-center sm:text-left text-primary">
                        Registo de Membros
                    </h1>
                </div>

                {/* Card de erro */}
                <div className="max-w-2xl mx-auto">
                    <Card className="border-2 shadow-lg border-orange-200">
                        <CardHeader className="text-center pb-6">
                            <div className="flex justify-center mb-4">
                                <div className="bg-orange-100 p-4 rounded-full">
                                    <AlertTriangle className="h-16 w-16 text-orange-600" />
                                </div>
                            </div>
                            <CardTitle className="text-3xl font-bold text-orange-700 mb-2">
                                ⚠️ Dados Já Registados
                            </CardTitle>
                            <CardDescription className="text-xl">
                                Alguns dos dados inseridos já se encontram no sistema
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6 text-center">
                            <div className="bg-orange-50 p-6 rounded-lg border border-orange-200">
                                <h3 className="text-xl font-semibold mb-4 text-orange-800">
                                    🔍 Possíveis motivos:
                                </h3>
                                <ul className="text-left text-gray-700 space-y-2 mb-4 max-w-md mx-auto">
                                    <li className="flex items-start gap-2">
                                        <span className="text-orange-600 mt-1">•</span>
                                        <span>NIF já registado no sistema</span>
                                    </li>
                                    <li className="flex items-start gap-2">
                                        <span className="text-orange-600 mt-1">•</span>
                                        <span>Número de telefone já em uso</span>
                                    </li>
                                </ul>
                                <p className="text-orange-700 font-medium">
                                    Pode já ser membro registado ou ter dados conflituosos
                                </p>
                            </div>

                            <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
                                <h3 className="text-xl font-semibold text-blue-800 mb-4">
                                    📞 Contacte o Nosso Suporte
                                </h3>
                                <p className="text-blue-700 mb-4 leading-relaxed">
                                    Para resolver esta situação e esclarecer o seu registo, 
                                    entre em contacto connosco através dos seguintes meios:
                                </p>
                                <div className="space-y-3 text-blue-700">
                                    <div className="flex items-center justify-center gap-3">
                                        <Mail className="h-5 w-5 flex-shrink-0" />
                                        <span className="font-medium"><EMAIL></span>
                                    </div>
                                </div>
                            </div>

                            <div className="flex flex-col sm:flex-row gap-3 justify-center">
                                <Button
                                    onClick={handleTryAgain}
                                    variant="outline"
                                    size="lg"
                                    className="font-semibold"
                                >
                                    Tentar Novamente
                                </Button>
                                <Button
                                    onClick={handleGoToPortal}
                                    size="lg"
                                    className="font-semibold bg-primary hover:bg-primary/90 text-white"
                                >
                                    <Home className="h-4 w-4 mr-2" />
                                    Ir para o Portal
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </main>
    )
}
