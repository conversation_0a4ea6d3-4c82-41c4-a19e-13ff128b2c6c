"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog"
import { AlertTriangle, Mail, Phone } from "lucide-react"

interface RegistrationConflictModalProps {
    isOpen: boolean
    onClose: () => void
}

export function RegistrationConflictModal({ isOpen, onClose }: RegistrationConflictModalProps) {
    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-md">
                <DialogHeader className="text-center">
                    <div className="flex justify-center mb-4">
                        <div className="bg-orange-100 p-3 rounded-full">
                            <AlertTriangle className="h-12 w-12 text-orange-600" />
                        </div>
                    </div>
                    <DialogTitle className="text-2xl font-bold text-orange-700">
                        ⚠️ Dados Já Registados
                    </DialogTitle>
                    <DialogDescription className="text-lg text-gray-600 mt-4">
                        Alguns dos dados inseridos já se encontram registados no sistema
                    </DialogDescription>
                </DialogHeader>
                
                <div className="space-y-6 mt-6">
                    <div className="bg-orange-50 p-4 rounded-lg border border-orange-200">
                        <h3 className="font-semibold text-orange-800 mb-2">
                            🔍 Possíveis motivos:
                        </h3>
                        <ul className="text-sm text-orange-700 space-y-1 list-disc list-inside">
                            <li>Email já associado a outra conta</li>
                            <li>NIF já registado no sistema</li>
                            <li>Número de telefone já em uso</li>
                        </ul>
                    </div>

                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                        <h3 className="font-semibold text-blue-800 mb-3">
                            📞 Contacte o Suporte
                        </h3>
                        <p className="text-sm text-blue-700 mb-3">
                            Para resolver esta situação, entre em contacto connosco:
                        </p>
                        <div className="space-y-2 text-sm text-blue-700">
                            <div className="flex items-center gap-2">
                                <Mail className="h-4 w-4 flex-shrink-0" />
                                <span><EMAIL></span>
                            </div>
                            <div className="flex items-center gap-2">
                                <Phone className="h-4 w-4 flex-shrink-0" />
                                <span>+351 XXX XXX XXX</span>
                            </div>
                        </div>
                    </div>

                    <Button
                        onClick={onClose}
                        className="w-full"
                        size="lg"
                    >
                        Entendido
                    </Button>
                </div>
            </DialogContent>
        </Dialog>
    )
}
