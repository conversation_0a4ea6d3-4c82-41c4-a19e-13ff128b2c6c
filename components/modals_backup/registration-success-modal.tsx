"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog"
import { CheckCircle, Home } from "lucide-react"

interface RegistrationSuccessModalProps {
    isOpen: boolean
    onClose: () => void
}

export function RegistrationSuccessModal({ isOpen, onClose }: RegistrationSuccessModalProps) {
    const handleGoToPortal = () => {
        window.location.href = 'https://portalchega.pt/'
    }

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-md">
                <DialogHeader className="text-center">
                    <div className="flex justify-center mb-4">
                        <div className="bg-green-100 p-3 rounded-full">
                            <CheckCircle className="h-12 w-12 text-green-600" />
                        </div>
                    </div>
                    <DialogTitle className="text-2xl font-bold text-green-700">
                        ✅ Registo Concluído!
                    </DialogTitle>
                    <DialogDescription className="text-lg text-gray-600 mt-4">
                        O seu registo foi efetuado com sucesso. Bem-vindo ao Chega!
                    </DialogDescription>
                </DialogHeader>
                
                <div className="space-y-6 mt-6">
                    <div className="bg-green-50 p-4 rounded-lg border border-green-200 text-center">
                        <p className="text-green-800 font-medium mb-2">
                            🎉 Parabéns!
                        </p>
                        <p className="text-sm text-green-700">
                            Pode agora aceder ao portal dos membros para explorar todas as funcionalidades disponíveis.
                        </p>
                    </div>

                    <div className="flex flex-col gap-3">
                        <Button
                            onClick={handleGoToPortal}
                            size="lg"
                            className="w-full bg-primary hover:bg-primary/90 text-white font-semibold"
                        >
                            <Home className="h-4 w-4 mr-2" />
                            Ir para o Portal
                        </Button>
                        
                        <Button
                            onClick={onClose}
                            variant="outline"
                            size="lg"
                            className="w-full"
                        >
                            Fechar
                        </Button>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    )
}
