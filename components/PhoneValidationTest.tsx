"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { isValidPhoneNumber, isPossiblePhoneNumber } from 'libphonenumber-js'
import { country_code } from "@/components/forms/fields/constants"

// Criar mapeamento de código de país para ISO
const countryCodeToISO: Record<string, string> = {}
country_code.forEach(item => {
    countryCodeToISO[item.code] = item.iso.toUpperCase()
})

// Funções de validação (iguais ao schema.ts)
function validatePhoneNumberPossible(phoneNumber: string, countryCode: string): boolean {
    if (!phoneNumber || !countryCode) return false;

    try {
        const isoCode = countryCodeToISO[countryCode];
        
        if (isoCode) {
            // Usar código ISO se disponível
            return isPossiblePhoneNumber(phoneNumber, isoCode as any);
        } else {
            // Fallback para formato internacional completo
            const fullNumber = countryCode + phoneNumber;
            return isPossiblePhoneNumber(fullNumber);
        }
    } catch {
        return false;
    }
}

function validatePhoneNumberValid(phoneNumber: string, countryCode: string): boolean {
    if (!phoneNumber || !countryCode) return false;
    
    try {
        const isoCode = countryCodeToISO[countryCode];
        
        if (isoCode) {
            // Usar código ISO se disponível
            return isValidPhoneNumber(phoneNumber, isoCode as any);
        } else {
            // Fallback para formato internacional completo
            const fullNumber = countryCode + phoneNumber;
            return isValidPhoneNumber(fullNumber);
        }
    } catch {
        return false;
    }
}

export default function PhoneValidationTest() {
    const [phoneNumber, setPhoneNumber] = useState("")
    const [countryCode, setCountryCode] = useState("+351")
    const [results, setResults] = useState<any>(null)

    const handleTest = () => {
        try {
            const isoCode = countryCodeToISO[countryCode];
            const isPossible = validatePhoneNumberPossible(phoneNumber, countryCode);
            const isValid = validatePhoneNumberValid(phoneNumber, countryCode);

            // Teste direto com as funções da biblioteca
            let directIsPossible = false;
            let directIsValid = false;
            
            try {
                if (isoCode) {
                    directIsPossible = isPossiblePhoneNumber(phoneNumber, isoCode as any);
                    directIsValid = isValidPhoneNumber(phoneNumber, isoCode as any);
                } else {
                    const fullNumber = countryCode + phoneNumber;
                    directIsPossible = isPossiblePhoneNumber(fullNumber);
                    directIsValid = isValidPhoneNumber(fullNumber);
                }
            } catch (e) {
                // Ignore errors for direct test
            }

            setResults({
                isoCode,
                isPossible,
                isValid,
                directIsPossible,
                directIsValid,
                fullNumber: isoCode ? phoneNumber : countryCode + phoneNumber,
                error: null
            });
        } catch (error: any) {
            setResults({
                error: error.message,
                isPossible: false,
                isValid: false
            });
        }
    }

    const countryOptions = country_code.slice(0, 20) // Mostrar apenas os primeiros 20 para teste

    return (
        <div className="container mx-auto py-10 px-4">
            <Card className="max-w-2xl mx-auto">
                <CardHeader>
                    <CardTitle>Teste de Validação de Telefone</CardTitle>
                    <CardDescription>
                        Teste a validação de números de telefone com isValid e isPossible
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                    <div className="space-y-4">
                        <div>
                            <Label htmlFor="countryCode">Código do País</Label>
                            <Select value={countryCode} onValueChange={setCountryCode}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Selecione o código do país" />
                                </SelectTrigger>
                                <SelectContent>
                                    {countryOptions.map((country) => (
                                        <SelectItem key={country.code} value={country.code}>
                                            {country.code} ({country.country}) - ISO: {country.iso.toUpperCase()}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                        
                        <div>
                            <Label htmlFor="phoneNumber">Número de Telefone</Label>
                            <Input
                                id="phoneNumber"
                                value={phoneNumber}
                                onChange={(e) => setPhoneNumber(e.target.value)}
                                placeholder="Ex: 912345678"
                                type="tel"
                            />
                        </div>

                        <Button onClick={handleTest} disabled={!phoneNumber.trim()}>
                            Testar Validação
                        </Button>
                    </div>

                    {results && (
                        <div className="space-y-4">
                            <h3 className="text-lg font-semibold">Resultados:</h3>
                            
                            <div className="bg-gray-50 p-4 rounded-lg">
                                <div className="space-y-2">
                                    <p><strong>Código ISO:</strong> {results.isoCode || "N/A"}</p>
                                    <p><strong>Número Testado:</strong> {results.fullNumber || "N/A"}</p>
                                    
                                    <div className="border-t pt-2 mt-2">
                                        <p className="font-semibold">Validação via Funções do Schema:</p>
                                        <p><strong>É Possível:</strong> {results.isPossible ? "✅ Sim" : "❌ Não"}</p>
                                        <p><strong>É Válido:</strong> {results.isValid ? "✅ Sim" : "❌ Não"}</p>
                                    </div>
                                    
                                    <div className="border-t pt-2 mt-2">
                                        <p className="font-semibold">Validação Direta da Biblioteca:</p>
                                        <p><strong>É Possível (Direto):</strong> {results.directIsPossible ? "✅ Sim" : "❌ Não"}</p>
                                        <p><strong>É Válido (Direto):</strong> {results.directIsValid ? "✅ Sim" : "❌ Não"}</p>
                                    </div>
                                    
                                    {results.error && (
                                        <p><strong>Erro:</strong> <span className="text-red-600">{results.error}</span></p>
                                    )}
                                </div>
                            </div>

                            <div className="bg-blue-50 p-4 rounded-lg">
                                <h4 className="font-semibold mb-2">Exemplos de Teste:</h4>
                                <div className="text-sm space-y-1">
                                    <p><strong>Portugal (+351):</strong> 912345678, 213456789</p>
                                    <p><strong>Brasil (+55):</strong> 11987654321</p>
                                    <p><strong>EUA (+1):</strong> 2025551234</p>
                                </div>
                            </div>
                        </div>
                    )}
                </CardContent>
            </Card>
        </div>
    )
}
