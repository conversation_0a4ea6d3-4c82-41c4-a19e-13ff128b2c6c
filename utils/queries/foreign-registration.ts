import {useMutation} from "@tanstack/react-query"
import {postForeignRegistration} from "@/utils/requests/foreign-registration";

export const useRegisterForeignUser = () => {
    return useMutation({
        mutationFn: postForeignRegistration,
        onSuccess: (data) => {
            console.log('Foreign registration successful:', data)
        },
        onError: (error) => {
            console.error('Foreign registration failed:', error)
        }
    });
}
