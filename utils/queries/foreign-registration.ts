import {useMutation} from "@tanstack/react-query"
import {postForeignRegistration} from "@/utils/requests/foreign-registration";

export const useRegisterForeignUser = () => {
    return useMutation({
        mutationFn: postForeignRegistration,
        onSuccess: (data) => {
            console.log('Foreign registration successful:', data)
        },
        onError: (error: any) => {
            // Não logar erro 409 (dados duplicados) pois é comportamento normal
            if (error?.response?.status !== 409) {
                console.error('Foreign registration failed:', error)
            }
        }
    });
}
