import {RegisterCredentials, ValidationRequest} from "@/utils/types/user";
import axios from "axios"

export const registerUser = async (credentials: RegisterCredentials) => {
    const response = await axios.post(`https://portalchega.pt/api/registrations/finish-registration/`, credentials)
    return response.data
}

export const validateRegistration = async (identifier: string, foreign?: boolean) => {
    const params: any = { identifier }
    if (foreign !== undefined) {
        params.foreign = foreign
    }

    const response = await axios.get<ValidationRequest>(`https://portalchega.pt/api/registrations/validate-registration/`, {
        params
    })
    return response.data
}
